/*
 * File: bulk-detail.component.ts
 * Project: elan-web
 * File Created: Monday, 26th September 2022 7:33:31 pm
 * Author: liucp
 * Description:
 * -----
 * Last Modified: Tuesday, 13th June 2023 4:16:05 pm
 * Modified By: liucp
 */
import { ChangeDetectorRef, Component, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { format, startOfDay } from 'date-fns';
import {
  FlcColorSizeTableChangeEvent,
  FlcColorSizeTableComponent,
  FlcDrawerHelperService,
  FlcModalService,
  FlcUtilService,
  FlcValidatorService,
} from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzModalService } from 'ng-zorro-antd/modal';
import { BulkService } from '../bulk.service';
import { detailModel, LinesType, processItem } from '../models/bulk-detail.interface';
import { IsMerchandiserEnum, OrderStatus, PageModeEnum, SourceTypeEnum } from '../models/bulk.enum';
import { FlcColorSizeTableCell, FlcSpUtilService, BaseInfoCustomContainerComponent } from 'fl-common-lib';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { max } from 'lodash';
import { debounceTime, finalize, Subject, firstValueFrom, switchMap, distinctUntilChanged } from 'rxjs';
import { BulkPrintComponent } from 'src/app/core/order/bulk/bulk-print/bulk-print.component';
import { initBasicList, initDFWOConfigList } from './bulk-detail.config';
import { factoryCodes } from 'src/app/shared/common-config';

@Component({
  selector: 'app-bulk-detail',
  templateUrl: './bulk-detail.component.html',
  styleUrls: ['./bulk-detail.component.scss'],
})
export class BulkDetailComponent implements OnInit {
  @ViewChild('fileGallery') fileGallery: any;
  @ViewChild('imageGallery') imageGallery: any;
  @ViewChild('basicBox') basicBox: any;
  @ViewChild('colorSizeTableSum') colorSizeTableSum: any;
  @ViewChildren('colorSizeTable') colorSizeTable!: QueryList<FlcColorSizeTableComponent>;
  @ViewChild('bulkPrint') bulkPrint!: BulkPrintComponent;
  translateName = 'bulk.';
  lang = this._service.lang;

  // 颜色和尺码选项
  colorOptions: any[] = [];
  sizeOptions: any[] = [];
  isSizeOptionsLoading = false;
  loading = false; // 页面加载
  headerTopTitle!: string;
  id!: number | string;
  edit!: boolean;
  poSelectList: any[] = []; // 选择交付单下拉列表
  poValue = ''; // 选择的po值
  poNumber = 0;
  detail: detailModel | any = {
    io_basic: {
      commit: false,
      style_code: '',
      io_code: '',
      customer: '',
      customer_id: 0,
      category: '',
      brand_id: null,
      order_category: 1,
      order_production_type: 1,
      first_material_name: '',
      first_material_code: '',
      first_material_id: 0,
      second_material_name: '',
      second_material_code: '',
      second_material_id: 0,
      third_material_id: 0,
      third_material_name: '',
      third_material_code: '',
      order_date: '',
      contract_number: '',
      extra_process_info: [],
      remark: '',
      order_pictures: [],
      appendix_requirements: [],
      customer_io_code: '',
      deletable: true,
      order_status: 0,
      order_status_value: '',
      reason: '',
      production_type: 3,
      store: '',
      company_code: '',
      composition: '',
      department: '',
      gen_dept_id: 0,
      contract_price: '',
      fabric: '',
      color: '',
      sample_requirement: '',
      printing_embroidery: '',
      sew_accessory: '',
      consolidation_accessory: '',
      accessory_remark: '',
      special_requirement: '',
      pictures: [],
      upfront_payment_rate: '',
      currency_id: '',
      currency_name: '',
      payment_id: '',
    },
    io_lines: [],
    pos: [],
  }; // 详情数据
  basicList = initBasicList();

  bulkForm!: FormGroup; // 表单
  fileList: any = []; // 图片
  showTop = true; // 是否展示上半部分
  toolTipVisible = true; // 是否展示toolTipVisible
  tabIndex = 0; // 当前选择的tab
  extraList: any[] = []; // 二次工艺数组
  order_outgoing_options: any[] = [
    // 订单类型
    { label: '首单', value: 1 },
    { label: '翻单', value: 2 },
  ];
  order_production_options: any[] = [
    // 生产类型
    { label: '外发加工', value: 1 },
    { label: '成品采购', value: 2 },
  ];
  quality_level_options = [
    // 品质
    { label: '高（精品货）', value: 1 },
    { label: '中（一般品牌货）', value: 2 },
    { label: '低（市场货）', value: 3 },
  ];
  payment_condition_options = [
    // 付款条件
    { label: '50%首付、50%尾款', value: '50%首付、50%尾款' },
    { label: '30%首付、70%尾款', value: '30%首付、70%尾款' },
    { label: '30%首付、60%中期款到发货、10%尾款', value: '30%首付、60%中期款到发货、10%尾款' },
  ];
  regionList: any[] = []; // 省市区列表
  specSizeList: any[] = []; // 尺码组
  styleList: any[] = []; // 款式分类
  brandList: any[] = []; // 款式分类
  orderClassificationList: any[] = []; // 销售渠道
  sale_channel_options: any[] = []; // 销售渠道

  payment_id_options: any[] = [];
  currency_id_options: any[] = [];
  departmentList: any[] = []; // 部门
  selectStyle: any[] = []; // 选中的款式分类
  statusEnum = OrderStatus; // 状态枚举值
  isVisible = false; // 退回修改弹窗
  isConfirm = false; // 是否点击了提交
  refuseReason = ''; // 退回原因
  comLoading = false; // 弹窗确定Loading
  deletePos: any[] = []; // 删除的po
  deletePoLines: any[] = []; // 删除的po_ines
  originDetail: any; //  原始详情数据
  prodData: any; // 成衣加工数据
  outData: any; // 二次工艺数据
  copyId?: number; // 复制的id
  styleCodeList: any = []; // 款式编码模糊搜索列表
  styleCodeBlurFlag = false; // 款式编码是否失去焦点
  styleCodeDetail: any = {}; // 款式编码详情
  styleCodeId!: number; // 款式编码id
  bulkOrderCode = null; // 大货单号
  styleLibPayload$ = new Subject<{
    limit: number;
    page: number;
    column: string;
    value: any;
  }>();
  printVisible = false;
  printTitle = '标签全打';
  printType = 1;
  selectedIds: any = [];
  bulk_order_card = [];
  bulk_id: any = null;
  isFold = false; //接单信息展开收起
  history_id = this._route.snapshot.queryParamMap.get('history_id');

  customBaseInfoConfig = {
    localKey: 'bulkDetailBaseInfo',
    version: '1.3.0',
    translateName: this.translateName,
  };
  sourceTypeEnum = SourceTypeEnum;
  DFWOConfigList = initDFWOConfigList();
  selectOptions: any = {
    season: [],
    lead_time: [],
    origin_country: [],
    pro_office: [],
    category_type: [],
    customer: [],
    shipment_port: [],
    order_labels: [],
  };
  userInfo: any = {};
  factoryCodes = factoryCodes || [];
  PageModeEnum = PageModeEnum;
  pageMode = PageModeEnum.new;

  // 在类定义中添加标志位
  private isCalculating = false;
  processedData: any[] = [];
  tableTotals: any = [[]];
  private valueChangeSubject = new Subject<{ value: any; code: string }>();

  // 添加合计表格数据属性
  mergedTableData: any[] = [];
  mergedTotals = {
    qty: 0,
    sku_amount: 0,
    excess_quantity: 0,
    deficiency_quantity: 0,
  };

  constructor(
    public _storage: AppStorageService,
    private _route: ActivatedRoute,
    private _notice: NzNotificationService,
    private _cdr: ChangeDetectorRef,
    private _fb: FormBuilder,
    private _msg: NzMessageService,
    private _router: Router,
    private _validator: FlcValidatorService,
    private _flcModalService: FlcModalService,
    private notification: NzNotificationService,
    public _service: BulkService,
    private _flcUtil: FlcUtilService,
    private _spUtil: FlcSpUtilService,
    private _drawer: FlcDrawerHelperService,
    private modal: NzModalService
  ) {
    const userInfo: string | null = localStorage.getItem('userInfo');
    this.userInfo = userInfo ? JSON.parse(userInfo) : {};
    this._service.fieldArr = this._storage.getFieldActions('order/bulk');
  }

  can_edit_field(name: string) {
    const all_fields = this._service.fieldArr?.map((field: any) => {
      return field?.fieldName;
    });
    return all_fields.includes(name)
      ? this._service.fieldArr?.find((field: any) => field.fieldName === name && field?.editPermission === 2)
      : true;
  }

  async ngOnInit(): Promise<void> {
    this.initBulkConfig();
    this.debounceList();
    this.processDataForTable();
    this.initColorAndSizeOptions();
    this._service.btnArr = this._storage.getUserActions('order/bulk');
    this.id = this._route.snapshot.paramMap.get('id') || '';
    this.copyId = Number(this._route.snapshot.queryParamMap.get('copyId') || 0);
    this.headerTopTitle = this.id === 'new' ? '新建订单需求' : '订单需求详情';
    if (this.id === 'new' && !this.copyId) {
      this.pageMode = PageModeEnum.new;
      this.edit = true;
      const { code, code_number } = this.generateCode();
      Object.assign(this.detail, {
        pos: [
          {
            editable: true,
            po_basic: {
              po_code: `${code}`,
              code_number: `${code_number}`,
              due_time: '',
              deletable: true,
              customer_due_time: '',
            },
            po_lines: [],
          },
        ],
      });

      this.getDefaultStylePicture(); // 填充默认图片
      if (this.factoryCodes.includes(this.userInfo?.factory_code)) {
        this.handleFillDefaultSizeGroup();
      }
      setTimeout(() => {
        (document.querySelector('.po-input') as HTMLInputElement).focus();
      }, 350);
      this.getRegions(); // 获取国家省市区
      this.getCascade(); // 获取款式分类
      this.getBrand(); // 获取品牌
      this.getSaleChannel(); // 获取销售渠道
      this.getPaymentId(); // 获取支付方式
      this.getCurrencyId(); // 获取币种
      this.getOrderClassification();
      this.getSeasons(); // 获取季节
      this.getLeadtime(); // 获取生产周期
      this.getOriginCountry(); // 获取生产国
      this.getProoffice(); // 获取办事处
      this.getCategoryType(); // 获取品类
      this.getCustomer(); // 获取客户
      this.getIncoTerms(); // 获取出货港
      this.getFactoryAndMerchandiser();
      this.handleForm();
      this.setCurrentBizUser();
      this.getUserBaseInfo(); // 设置默认部门
      this.getDepartment(); // 获取部门
      this.getOrderLabel(); // 订单标签

      const sales: any = this._spUtil.getObject('salesContractConvertOrderKey');
      if (sales) {
        this.bulkForm?.get('style_code')?.setValue(sales?.styleCode?.style_code ?? null);
        this.bulkForm?.get('style_lib_id')?.setValue(sales?.styleCode?.style_lib_id ?? null);
        this.styleCodeBlur();
        //po_lines
        const line = {
          po_basic: {
            spec_group_id: sales?.size_group?.size_group_id ?? null,
            spec_group_name: sales?.size_group?.size_group_name ?? null,
            spec_group_code: sales?.size_group?.size_group_code ?? null,
            deletable: true,
          },
          editable: true,
          po_lines: sales?.po_lines,
          delete_po_lines: [],
          change_po_lines: sales?.po_lines,
        };
        this.detail.pos[0] = line;
        this.sumTotal();
      }
    } else {
      this.pageMode = this.copyId ? PageModeEnum.copy : PageModeEnum.detail;
      this.loading = true;
      Promise.all([
        this.getRegions(),
        this.getCascade(),
        this.getBrand(),
        this.getSaleChannel(),
        this.getPaymentId(), // 获取支付方式
        this.getCurrencyId(), // 获取币种
        this.getOrderClassification(),
        this.getSeasons(), // 获取季节
        this.getLeadtime(), // 获取生产周期
        this.getOriginCountry(), // 获取生产国
        this.getProoffice(), // 获取办事处
        this.getCategoryType(), // 获取品类
        this.getCustomer(), // 获取客户
        this.getIncoTerms(), // 获取出货港
        this.getFactoryAndMerchandiser(),
        this.getDepartment(),
        this.getOrderLabel(), // 订单标签
      ]).then((result: any) => {
        this.getDetail(this.copyId ? Number(this.copyId) : Number(this.id), true);
      });
    }

    this.styleLibPayload$.next({
      column: 'style_code',
      value: '',
      limit: 99999,
      page: 1,
    });

    this.getExta();
    this.originDetail = JSON.parse(JSON.stringify(this.detail));
  }

  initBulkConfig() {
    const bulkConfig = JSON.parse(localStorage.getItem(this.customBaseInfoConfig.localKey + 'customBaseInfo') || 'false');
    if (bulkConfig && bulkConfig.version === this.customBaseInfoConfig.version) {
      this.basicList = bulkConfig.basicConfigList;
    }
    // 颜色尺码交付单
    const customDFWOConfig = JSON.parse(localStorage.getItem(this.customDFWOConfig.localKey + 'customBaseInfo') || 'false');
    if (customDFWOConfig && customDFWOConfig.version === this.customDFWOConfig.version) {
      this.DFWOConfigList = customDFWOConfig.basicConfigList;
    }
  }

  setCurrentBizUser() {
    const userInfoJSON = localStorage.getItem('userInfo');
    if (userInfoJSON) {
      const userInfo = JSON.parse(userInfoJSON);
      if (this.factoryCodes.includes(userInfo?.factory_code)) return;
      const employeeId = userInfo?.employee_id;
      if (employeeId) {
        this.bulkForm.get('biz_user_emp_id')?.setValue(`employ-${employeeId}`);
      }
    }
  }
  async getOrderClassification() {
    const res = await firstValueFrom(this._service.getDict({ column: 'ddlx' }));
    if (res.code === 200) {
      this.orderClassificationList = (res.data.option_list || []).map((d: any) => ({
        label: d.label,
        value: Number(d.value),
      }));
    }
  }
  ngAfterViewInit(): void {}
  getFileList(e: any) {
    this._service.isChange = true;
    this.detail.io_basic.appendix_requirements = e;
  }

  /**
   * 打印方法
   */
  onTagPrint(type: number) {
    this.bulk_order_card = [];
    this.getPrintOptions();
    this.printType = type;
    switch (type) {
      case 1:
        this.printTitle = this._service.translateValue(this.translateName + '唯一码标签全打');
        break;
      case 2:
        this.printTitle = this._service.translateValue(this.translateName + '唯一码标签补打');
        break;
    }
  }

  getPrintOptions() {
    this._service.getLabelOptions({ order_id_list: [this.bulk_id || this.id] }).subscribe((res: any) => {
      if (res) {
        this.bulk_order_card = res.data.bulk_order_card_list;
        this.printVisible = true;
        this.bulk_order_card.forEach((item: any) => {
          item.printOptionsOne = [];
        });
      }
    });
  }

  ngOnDestroy(): void {
    this._spUtil.removeObject('salesContractConvertOrderKey');
  }

  styleCodeBlur() {
    this.styleCodeBlurFlag = true;
    const styleCode = this.bulkForm.get('style_code')?.value;
    this.detail.io_basic.style_code = styleCode;
    this._service.getStyleCodeDetail(styleCode).subscribe(async (res) => {
      if (res.code === 200) {
        if (res.data) {
          this.styleCodeDetail = res.data;
          if (this.styleCodeDetail.brand_id) {
            const bulkCodeRes = await firstValueFrom(this._service.getBulkOrderCode(this.styleCodeDetail.brand_id));
            this.bulkOrderCode = bulkCodeRes.code === 200 ? bulkCodeRes.data.code : null;
          }
          Object.assign(this.detail.io_basic, {
            order_pictures: this.styleCodeDetail.style_pic_list,
            io_code: this.bulkOrderCode,
            style_code: styleCode,
            style_code_uuid: this.styleCodeDetail.uuid,
            brand_id: this.styleCodeDetail.brand_id,
            category: this.styleCodeDetail.category,
            styleOption: [
              this.styleCodeDetail.first_style_name,
              this.styleCodeDetail.second_style_name,
              this.styleCodeDetail.third_style_name,
            ],
            extra_process_list: (this.styleCodeDetail.extra_process || [])?.map((d: any) => d.id),
            season: this.styleCodeDetail?.season_name,
          });
          this.selectStyle = [
            {
              label: this.styleCodeDetail.first_style_name,
              value: this.styleCodeDetail.first_style_id,
              code: this.styleCodeDetail.first_style_code,
            },
            {
              label: this.styleCodeDetail.second_style_name,
              value: this.styleCodeDetail.second_style_id,
              code: this.styleCodeDetail.second_style_code,
            },
            {
              label: this.styleCodeDetail.third_style_name,
              value: this.styleCodeDetail.third_style_id,
              code: this.styleCodeDetail.third_style_code,
            },
          ];
          this.extraOpen();
          this.changeExtra(this.styleCodeDetail.extra_process);
          const { order_pictures, io_code, style_code, category, styleOption, extra_process_list, brand_id, pictures, season } =
            this.detail.io_basic;
          this.bulkForm.patchValue({
            brand_id,
            pictures,
            order_pictures,
            // style_code,
            io_code,
            category,
            style: styleOption,
            extra_process_info: extra_process_list,
            season,
          });
          this.autoPo();
          // 重新初始化颜色和尺码选项
          setTimeout(() => {
            this.initColorAndSizeOptions();
          }, 100);
        } else {
          this.styleCodeDetail = null;
        }
      }
    });
  }
  styleCodeFocus() {
    this.styleCodeBlurFlag = false;
    const value = this.bulkForm.get('style_code')?.value;
    const payload = {
      column: 'style_code',
      value: value,
      limit: 300,
      page: 1,
    };
    this.styleLibPayload$.next(payload);
  }
  styleCodeChange(e: any) {
    const payload = {
      column: 'style_code',
      value: (e.target as HTMLInputElement).value,
      limit: 300,
      page: 1,
    };
    this.styleLibPayload$.next(payload);
  }

  debounceList() {
    this.styleLibPayload$
      .pipe(
        debounceTime(200),
        switchMap((obj) => this._service.getStyleLib(obj))
      )
      .subscribe((res) => {
        if (res?.code === 200) {
          this.styleCodeList = [];
          res.data.option_list.forEach((item: any) => {
            this.styleCodeList.push({ label: item.style_code, value: item.id });
          });
        }
      });
  }
  /**
   * 交付日期修改更新视图
   */
  dateChange(e: any) {
    if (!e) {
      this._cdr.detectChanges();
    }
  }
  /**
   * 是否可离开当前页面
   */
  canLeave() {
    if (this.edit && (this.bulkForm.dirty || JSON.stringify(this.originDetail) !== JSON.stringify(this.detail))) {
      return false;
    } else {
      return true;
    }
  }
  /**
   * 获取二次工艺下拉
   */
  getExta() {
    // this._service.extraOption().subscribe((res: any) => {
    //   if (res.code === 200) {
    //     this.extraList = res.data.option_list || [];
    //   }
    // });
    this._service.getDict({ column: 'ecgy' }).subscribe((res: any) => {
      if (res.code === 200) {
        // value值转number
        res.data.option_list = (res.data.option_list || []).map((d: any) => ({
          label: d.label,
          value: Number(d.value),
        }));
        this.extraList = res.data.option_list;
      }
    });
  }
  /**
   * 款式分类下拉值改变
   */
  styleChange(e: any) {
    this.selectStyle = e;
  }
  handleDynamicSearch(e: any, item: any) {
    this.bulkForm.get(item.value)?.setValue(e?.selectLine?.value || 0);
    this.bulkForm.get(item.code)?.setValue(e?.selectLine?.label || null);
  }
  handleDynamicOptionChange(val: any, item: any) {
    const { value, code } = item;
    const v = this.bulkForm.get(value)?.value;
    const selectItem = val?.options?.find((item: any) => item.value === v);
    if (selectItem) {
      this.bulkForm?.get(value)?.setValue(selectItem.value ?? 0);
      this.bulkForm?.get(code)?.setValue(selectItem.label ?? '');
    } else {
      this.bulkForm?.get(value)?.reset();
      this.bulkForm?.get(code)?.setValue('');
    }
  }
  /**
   * tab改变时重绘表格
   */
  async tabIndexChange(i: number) {
    if (this.detail.pos[i]?.po_basic?.po_code !== this.poValue) {
      this.poValue = '';
    }
    this.colorSizeTable?.get(i)?.reRender();

    // 更新当前tab的尺码选项
    try {
      this.sizeOptions = await this.fetchSizeOptions('', true, i);
    } catch (error) {
      this.sizeOptions = [];
    }
  }
  /**
   * 款式分类下拉值
   */
  getCascade() {
    return new Promise((resolve) => {
      this._service.getCascade().subscribe((res: any) => {
        if (res.code === 200) {
          this.styleList = res?.data?.children || [];
          resolve(true);
        }
      });
    });
  }
  /**
   * 品牌下拉值
   */
  getBrand() {
    return new Promise((resolve) => {
      this._service.getBrandOption().subscribe((res: any) => {
        if (res.code === 200) {
          this.brandList = res?.data?.option_list || [];
          resolve(true);
        }
      });
    });
  }

  // 获取客户下拉
  getCustomer() {
    return new Promise((resolve) => {
      this._service.getCustomerOption({}).subscribe((res: any) => {
        if (res.code === 200) {
          this.selectOptions.customer = res.data.customers || [];
          resolve(true);
        }
      });
    });
  }

  // 获取出货港下拉
  getIncoTerms() {
    return new Promise((resolve) => {
      this._service.getDict({ column: 'incoterms' }).subscribe((res: any) => {
        if (res.code === 200) {
          this.selectOptions.shipment_port = res.data.option_list || [];
          resolve(true);
        }
      });
    });
  }

  // 获取季节
  getSeasons() {
    return new Promise((resolve) => {
      this._service.getDict({ column: 'jj' }).subscribe((res: any) => {
        if (res.code === 200) {
          this.selectOptions.season = res.data.option_list || [];
          resolve(true);
        }
      });
    });
  }
  // 获取生产周期
  getLeadtime() {
    return new Promise((resolve) => {
      this._service.getDict({ column: 'leadtime' }).subscribe((res: any) => {
        if (res.code === 200) {
          this.selectOptions.lead_time = res.data.option_list || [];
          resolve(true);
        }
      });
    });
  }
  // 获取生产国
  getOriginCountry() {
    return new Promise((resolve) => {
      this._service.getDict({ column: 'origincountry' }).subscribe((res: any) => {
        if (res.code === 200) {
          this.selectOptions.origin_country = res.data.option_list || [];
          resolve(true);
        }
      });
    });
  }
  // 获取办事处
  getProoffice() {
    return new Promise((resolve) => {
      this._service.getDict({ column: 'prooffice' }).subscribe((res: any) => {
        if (res.code === 200) {
          this.selectOptions.pro_office = res.data.option_list || [];
          resolve(true);
        }
      });
    });
  }

  // 获取品类下拉
  getCategoryType() {
    return new Promise((resolve) => {
      this._service.getCategoryType({ column: 'second_style_name' }).subscribe((res: any) => {
        if (res.code === 200) {
          this.selectOptions.category_type = res.data.option_list || [];
          resolve(true);
        }
      });
    });
  }

  /** 销售渠道下拉数据 */
  getSaleChannel() {
    return new Promise((resolve) => {
      this._service.getDict({ column: 'xsqd' }).subscribe((res: any) => {
        if (res.code === 200) {
          this.sale_channel_options = (res.data.option_list || []).map((d: any) => ({
            label: d.label,
            code: d.code,
            value: Number(d.value),
          }));
          resolve(true);
        }
      });
    });
  }

  /**支付方式下拉数据 */
  getPaymentId() {
    return new Promise((resolve) => {
      this._service.getDict({ column: 'settlement_method' }).subscribe((res: any) => {
        if (res.code === 200) {
          this.payment_id_options = res.data.option_list || [];
          resolve(true);
        }
      });
    });
  }
  getCurrencyId() {
    return new Promise((resolve) => {
      this._service
        .getUnit({
          column: 'name',
          value: '',
          limit: 99999,
          page: 1,
          type: [9],
        })
        .subscribe((res: any) => {
          if (res.code === 200) {
            this.currency_id_options = res.data.option_list['9'] || [];
            resolve(true);
          }
        });
    });
  }
  // 获取订单标签
  getOrderLabel() {
    return new Promise((resolve) => {
      this._service.getDict({ column: 'ddbq' }).subscribe((res: any) => {
        if (res.code === 200) {
          this.selectOptions.order_labels = res.data.option_list || [];
          resolve(true);
        }
      });
    });
  }

  // 加工厂、跟单员
  original_process_factory_options: any[] = [];
  original_merchandiser_options: any[] = [];
  process_factory_options: any[] = [];
  merchandiser_options: any[] = [];
  getFactoryAndMerchandiser() {
    return new Promise((resolve) => {
      this._service.getFactoryAndMerchandiser().subscribe((res: any) => {
        if (res.code === 200) {
          this.original_process_factory_options = res.data?.factory_list || [];
          this.original_merchandiser_options = res.data?.merchandiser_list || [];
          this.merchandiser_options = res.data?.merchandiser_list || [];
          this.process_factory_options = res.data?.factory_list || [];
          this.check_is_merchandiser();
          resolve(true);
        }
      });
    });
  }

  // 新建和复制下，判断当前员工是否是跟单员, 是，则跟单员填充当前登录人,且不可编辑
  // 若先选了加工厂，再选跟单员时，只能选该工厂关联的跟单员；若先选了跟单员，则只能选他所负责的加工厂；VENDOR_NAME账号新建的订单，跟单员默认填充当前账号，且不可修改；
  is_merchandiser = false;
  check_is_merchandiser() {
    const op = this.original_merchandiser_options.find((item: any) => item?.is_self === IsMerchandiserEnum.yes);
    this.is_merchandiser = op;
    if (op) {
      this.bulkForm?.get('merchandiser_user_id')?.setValue(op?.user_emp_id, { emitViewToModelChange: false });
      this.bulkForm?.get('merchandiser_name')?.setValue(op?.user_name, { emitViewToModelChange: false });
      this.process_factory_options = op?.factory_list || [];
    }
  }

  /**
   * 打开跟单员下拉框
   * 1、判断当前登陆人是不是跟单员
   * 2、是，若若加工厂没值， 则下拉里只有这一项， 工厂也只能选择该跟单员的工厂， 若有工厂， 判断当前登陆人是不是该工厂下的跟单员， 是，则下拉只有这一项，不是，则为空
   * 2、否， 若加工厂没值， 则跟单员下拉是全部数据，有加工厂， 则下拉是该工厂下的跟单员集合
   */

  openMerchandiserUser(isOpen: boolean) {
    if (isOpen) {
      const is_mer: any = this.original_merchandiser_options.find((item: any) => item?.is_self === IsMerchandiserEnum.yes);
      const process_factory_code = this.bulkForm.get('process_factory_code')?.value;
      const merchandiser_list: any =
        this.original_process_factory_options.find((d: any) => d.process_factory_code === process_factory_code)?.merchandiser_list || [];

      if (is_mer) {
        // 有没有加工厂
        if (process_factory_code) {
          // 当前登陆人是跟单员,  看是不是当前工厂下的跟单员， 是当前工厂下的跟单员, 则下拉只有这一条数据， 否则，下拉为空
          const merchandiser = merchandiser_list.find((item: any) => item?.is_self === IsMerchandiserEnum.yes);
          this.merchandiser_options = merchandiser ? [{ ...merchandiser }] : [];
        } else {
          this.merchandiser_options = is_mer ? [{ ...is_mer }] : [];
        }
      } else {
        // 看当前登陆人不是跟单员，则下拉展示该工厂下的所有跟单元
        this.merchandiser_options = process_factory_code ? merchandiser_list : [...this.original_merchandiser_options];
      }
    }
  }

  openProcessFactory(e: boolean) {
    if (e) {
      const merchandiser_user_id = this.bulkForm.get('merchandiser_user_id')?.value;
      if (merchandiser_user_id) {
        this.process_factory_options =
          this.original_merchandiser_options.find((d: any) => d.user_emp_id === merchandiser_user_id)?.factory_list || [];
      } else {
        this.process_factory_options = [...this.original_process_factory_options];
      }
    }
  }

  // 生产类型切换
  orderProductionTypeChange(e: any) {
    const visible = e === 1; // 外发加工才显示下列字段
    // const codes = ['process_factory_code', 'merchandiser_user_id', 'pre_material_completed_time', 'biz_user_emp_id'];
    //  排除物料齐套时间、外发加工厂、业务员
    const codes = ['merchandiser_user_id'];

    // 取配置的状态，若配置关闭则不修改状态
    const savedStatus: any = {};
    const bulkConfig = JSON.parse(localStorage.getItem(this.customBaseInfoConfig.localKey + 'customBaseInfo') || 'false');

    if (bulkConfig && bulkConfig.version === this.customBaseInfoConfig.version) {
      const list = bulkConfig.basicConfigList;
      list.forEach((item: any) => {
        codes.includes(item.code) && (savedStatus[item.code] = item.visible);
      });
    }

    this.basicList.forEach((item) => {
      codes.includes(item.code) &&
        (savedStatus[item.code] || !(bulkConfig && bulkConfig.version === this.customBaseInfoConfig.version)) &&
        (item.visible = visible);
    });

    if (!visible) {
      codes.forEach((d) => {
        this.bulkForm?.get(d)?.setValue(null);
      });
    }
  }

  // 付款方式切换
  paymentMethodChange(e: any) {
    let payment_name = this.payment_id_options.find((item: any) => item.value === e)?.label;
    this.bulkForm?.get('payment_name')?.setValue(payment_name);
  }
  // 币种切换
  currencyChange(e: any) {
    let currency_name = this.currency_id_options.find((item: any) => item.value === e)?.label;
    this.bulkForm?.get('currency_name')?.setValue(currency_name);
  }
  //  新建,款式图填充默认图
  // 获取默认图片
  defaultPictureUrl = '';
  getDefaultStylePicture() {
    this._service.getDefaultStylePicture({})?.subscribe((res: any) => {
      if (res?.code === 200) {
        this.defaultPictureUrl = res?.data?.url;
        this.detail.io_basic.order_pictures = this.defaultPictureUrl ? [{ url: this.defaultPictureUrl, name: '', version: '' }] : [];
      }
    });
  }

  // 获取所有尺码组
  // 找到通用尺码组，通用尺码填充默认值，找不到，就不填充
  commonSizeGroup: any = null;
  commonSize: any = null;
  handleFillDefaultSizeGroup(i = 0) {
    this._service.getSizeGroup({}).subscribe((res: any) => {
      if (res?.code === 200) {
        // 默认填充通用尺码组
        const op: any = res?.data?.option_list?.find((s: any) => s?.label === 'Common Group');
        this.commonSizeGroup = op;
        this.setSizeTable(i);
      }
    });
  }

  setSizeTable(i: number) {
    if (this.commonSizeGroup) {
      this.sizeGroupChange({ selectLine: { ...this.commonSizeGroup }, value: this.commonSizeGroup?.value }, i);
      this._service.getSizeOptions({ size_group_id: this.commonSizeGroup?.value }).subscribe((res: any) => {
        if (res?.code === 200) {
          const sizeOp: any = res?.data?.option_list?.find((s: any) => s.label === 'Common');
          if (sizeOp) {
            this.colorSizeTable?.get(i)?.getSizeOption(true);
            setTimeout(() => {
              this.colorSizeTable?.get(i)?.onSizeValueChange(sizeOp?.value, 0);
            }, 1000);
          }
        }
      });
    }
  }

  sizeGroupChange(e: any, i: number) {
    if (e.value) {
      const data = e.selectLine;
      this.detail.pos[i].po_basic['spec_group_id'] = Number(data.value);
      this.detail.pos[i].po_basic['spec_group_name'] = data.label;
      this.detail.pos[i].po_basic['spec_group_code'] = data.code;
      this.handleDeleLine(i);
      this.findCommon();

      // 如果没有数据，创建一个空行
      this.ensureEmptyRowExists(i);
    } else {
      this.detail.pos[i].po_basic['spec_group_name'] = '';
      this.detail.pos[i].po_basic['spec_group_code'] = '';
      this.handleDeleLine(i);
      this.detail.pos[i].po_lines = [];
      if (this.detail.pos[i]?.change_po_lines) {
        this.detail.pos[i].change_po_lines = [];
      }
    }
    this.sumTotal();
    this.updateProcessedData(i);
  }
  handleDeleLine(i: number) {
    if (!this.detail.pos[this.tabIndex]['delete_po_lines']) {
      this.detail.pos[this.tabIndex]['delete_po_lines'] = [];
    }
    this.detail.pos[this.tabIndex]['delete_po_lines'] = [
      ...this.detail.pos[this.tabIndex]['delete_po_lines'],
      ...(this.detail.pos[i]['change_po_lines'] || []),
    ];
  }
  onFetchSizeOptions = (val?: string, allData?: boolean): Promise<any> => {
    return this.fetchSizeOptions(val, allData);
  };

  private fetchSizeOptions(val?: string, allData?: boolean, idx?: number): Promise<any> {
    return new Promise((resolve) => {
      const tabIndex = idx ?? this.tabIndex;
      const sizeGroupId = this.detail.pos[tabIndex]?.po_basic?.spec_group_id;
      const styleLibId = this.styleCodeDetail?.style_lib_id;
      const styleUuid = this.styleCodeDetail?.uuid;

      // 检查必要参数：如果没有尺码组ID或款式信息，直接返回空数组
      if (!sizeGroupId && !styleLibId && !styleUuid) {
        resolve([]);
        return;
      }

      const data = {
        column: 'size_name',
        limit: allData ? 9999 : 300,
        page: 1,
        value: val ?? '',
        size_group_id: sizeGroupId,
        style_lib_id: styleLibId,
        style_uuid: styleUuid,
      };

      this._service.getStyleLibSizeOnlyOption(data).subscribe({
        next: (res: any) => {
          if (res?.code === 200) {
            resolve(res.data?.option_list || []);
          } else {
            resolve([]);
          }
        },
        error: (error) => {
          resolve([]);
        },
      });
    });
  }
  fetchSizeOptionsHandler: Array<(val?: string, allData?: boolean) => Promise<any>> = [];
  onFetchSizeOptionsWrap(idx: number) {
    if (this.fetchSizeOptionsHandler[idx] === undefined) {
      this.fetchSizeOptionsHandler[idx] = (val?: string, allData?: boolean): Promise<any> => {
        return this.fetchSizeOptions(val, allData, idx);
      };
    }
    return this.fetchSizeOptionsHandler[idx];
  }
  /**
   * 查找不同尺码组相同的尺码
   */
  async findCommon() {
    const data = await this.onFetchSizeOptions('', true);
    const curData = this.detail.pos[this.tabIndex]['change_po_lines'] || [];
    const commonLine: any[] = [];

    curData.forEach((con: any) => {
      let flag = false;
      data.forEach((item: any) => {
        if (item.value === Number(con.size_info.spec_id)) {
          commonLine.push({ ...con, ...{ id: 0 }, line_uuid: '' });
          flag = true;
        }
      });
      if (!flag) {
        commonLine.push({ ...con, ...{ id: 0, size_info: { spec_code: '', spec_id: -con.indexing, spec_size: '' }, line_uuid: '' } });
      }
    });
    this.detail.pos[this.tabIndex]['po_lines'] = commonLine;
    this.detail.pos[this.tabIndex]['change_po_lines'] = commonLine;
    this.sumTotal();
  }
  /**
   * 尺码自动排序
   */
  async sortSize() {
    const data = await this.onFetchSizeOptions('', true);
    const curDataOrigin = this.detail.pos[this.tabIndex]['change_po_lines'];
    let curData = curDataOrigin.filter((item: any) => {
      return item.size_info.spec_id > 0;
    });
    const curData2 = curDataOrigin.filter((item: any) => {
      return item.size_info.spec_id < 0;
    });
    curData2.sort((a: any, b: any) => {
      return a.indexing - b.indexing;
    });
    const curData3: any = [];
    curData2.forEach((item: any, i: number) => {
      if (i === 0) {
        curData3.push([item]);
      } else {
        let flag = false;
        curData3.forEach((con: any) => {
          if (con[0].indexing === item.indexing) {
            con.push(item);
            flag = true;
          }
        });
        if (!flag) {
          curData3.push([item]);
        }
      }
    });
    // 使用新的indexing分配逻辑
    const sizeMap = new Map<number, number>(); // spec_id -> indexing
    let currentIndexing = 1;

    // 按照尺码选项的顺序为每个尺码分配indexing
    data.forEach((item: any) => {
      if (
        curData.findIndex((con: any) => {
          return item.value === Number(con.size_info.spec_id);
        }) > -1
      ) {
        if (!sizeMap.has(item.value)) {
          sizeMap.set(item.value, currentIndexing++);
        }
      }
    });

    // 为所有有效尺码的行分配indexing
    curData.forEach((con: any) => {
      const specId = con.size_info.spec_id;
      if (specId && specId > 0 && sizeMap.has(specId)) {
        con.indexing = sizeMap.get(specId);
      }
    });

    curData.sort((a: any, b: any) => {
      return a.indexing - b.indexing;
    });

    // 为无效尺码的行分配indexing
    let curData4: any = [];
    curData3.forEach((item: any, i: number) => {
      item.forEach((con: any) => {
        con.indexing = currentIndexing + i;
      });
      curData4 = [...curData4, ...item];
    });

    curData = [...curData, ...curData4];
    this.detail.pos[this.tabIndex]['po_lines'] = [...curData];
    this.detail.pos[this.tabIndex]['change_po_lines'] = [...curData];

    // 更新maxIndexing
    const maxIndexing = Math.max(currentIndexing - 1, curData3.length > 0 ? currentIndexing + curData3.length - 1 : 0);
    this.detail.pos[this.tabIndex]['maxIndexing'] = maxIndexing;

    this.sumTotal();
  }
  /**
   * 获取颜色组数据
   */
  onFetchColorOptions = (): Promise<any> => {
    return new Promise((resolve) => {
      this._service
        .getColorCascade({ style_lib_id: this.styleCodeDetail?.style_lib_id ?? null, style_uuid: this.styleCodeDetail?.uuid ?? null })
        .subscribe((res: any) => {
          resolve(res.data.children);
        });
    });
  };

  /**
   * 初始化颜色和尺码选项
   */
  async initColorAndSizeOptions() {
    // 等待样式代码详情加载完成
    if (!this.styleCodeDetail) {
      return;
    }

    // 初始化颜色选项
    try {
      this.colorOptions = await this.onFetchColorOptions();
    } catch (error) {
      this.colorOptions = [];
    }

    // 初始化尺码选项
    try {
      this.sizeOptions = await this.fetchSizeOptions('', true, this.tabIndex || 0);
    } catch (error) {
      this.sizeOptions = [];
    }
  }

  /**
   * 颜色选择器显示/隐藏事件
   */
  onShowColorSelector(isShow: boolean, tabIndex: number) {
    if (isShow && this.colorOptions.length === 0) {
      this.initColorAndSizeOptions();
    }
  }

  /**
   * 尺码选择器打开/关闭事件 - 参考flc-color-size-table的getSizeOption方法
   */
  getSizeOption(isOpen: boolean, tabIndex: number) {
    if (isOpen) {
      this.isSizeOptionsLoading = true;
      this.fetchSizeOptions('', true, tabIndex)
        .then((options) => {
          this.sizeOptions = options || [];
          this.isSizeOptionsLoading = false;
        })
        .catch(() => {
          this.sizeOptions = [];
          this.isSizeOptionsLoading = false;
        });
    }
  }

  /**
   * 更新change_po_lines中的数据
   */
  private updateChangePoLines(tabIndex: number) {
    if (!this.detail.pos[tabIndex].change_po_lines) {
      this.detail.pos[tabIndex].change_po_lines = [];
    }

    // 从processedData同步数据到change_po_lines，确保数据类型正确
    const processedData = this.processedData[tabIndex] || [];
    this.detail.pos[tabIndex].change_po_lines = processedData.map((item: any) => {
      const originalLine = { ...item.originalLine };

      // 确保关键字段的数据类型正确（转换为字符串）
      originalLine.unit_price = String(originalLine.unit_price || 0);
      originalLine.sku_amount = String(originalLine.sku_amount || 0);
      originalLine.excess_rate = String(originalLine.excess_rate || 0);
      originalLine.deficiency_rate = String(originalLine.deficiency_rate || 0);

      return originalLine;
    });
  }

  /**
   * 颜色选择变化事件 - 参考flc-color-size-table的实现
   * @param selectionPath 选择路径数组，类似 [{label: '颜色组', value: 'group1'}, {label: '红色', value: 'red'}]
   * @param item 当前行数据
   * @param tabIndex tab索引
   * @param rowIndex 行索引
   */
  onColorChange(selectionPath: any[], item: any, tabIndex: number, rowIndex: number) {
    if (selectionPath && selectionPath.length > 0) {
      // 获取最后一级的颜色信息（实际的颜色值）
      const selectedColor = selectionPath[selectionPath.length - 1];

      if (selectedColor) {
        // 获取当前行的原始颜色信息，用于查找相同颜色的其他行
        const originalColorId = item.originalLine.color_info.color_id;
        const originalColorName = item.originalLine.color_info.color_name;

        // 查找所有相同颜色的行
        const sameColorRows = this.processedData[tabIndex].filter((row: any) => {
          return row.originalLine.color_info.color_id === originalColorId && row.originalLine.color_info.color_name === originalColorName;
        });

        // 更新所有相同颜色的行
        sameColorRows.forEach((row: any) => {
          // 更新颜色信息 - 参考flc-color-size-table的onColorValueChange方法
          row.color = selectedColor.label;
          row.originalLine.color_info = {
            color_id: selectedColor.value,
            color_name: selectedColor.label,
            color_code: selectedColor.code || '',
          };
        });

        // 检查并删除重复的颜色+尺码组合（针对所有修改的行）
        sameColorRows.forEach((row: any, index: number) => {
          const currentRowIndex = this.processedData[tabIndex].indexOf(row);
          this.removeDuplicateColorSizeCombinations(tabIndex, row.originalLine, currentRowIndex);
        });

        // 更新change_po_lines中的数据
        this.updateChangePoLines(tabIndex);

        // 重新计算 rowspan，因为颜色变化可能影响分组
        this.updateProcessedData(tabIndex);

        // 触发数据变更事件，传递所有修改的行
        const affectedLines = sameColorRows.map((row: any) => row.originalLine);
        this.triggerTableDataChange('updateRow', tabIndex, affectedLines);
      }
    } else {
      // 清空选择 - 同样需要处理相同颜色的所有行
      const originalColorId = item.originalLine.color_info.color_id;
      const originalColorName = item.originalLine.color_info.color_name;

      // 查找所有相同颜色的行
      const sameColorRows = this.processedData[tabIndex].filter((row: any) => {
        return row.originalLine.color_info.color_id === originalColorId && row.originalLine.color_info.color_name === originalColorName;
      });

      // 清空所有相同颜色行的颜色信息
      sameColorRows.forEach((row: any) => {
        row.color = '';
        row.originalLine.color_info = {
          color_id: null,
          color_name: '',
          color_code: '',
        };
      });

      // 更新change_po_lines中的数据
      this.updateChangePoLines(tabIndex);

      // 重新计算 rowspan
      this.updateProcessedData(tabIndex);

      // 触发数据变更事件
      const affectedLines = sameColorRows.map((row: any) => row.originalLine);
      this.triggerTableDataChange('removeRow', tabIndex, affectedLines);
    }
  }

  /**
   * 尺码选择变化事件 - 参考flc-color-size-table的onSizeValueChange方法
   * @param sizeValue 选择的尺码值
   * @param item 当前行数据
   * @param tabIndex tab索引
   * @param rowIndex 行索引
   */
  onSizeChange(sizeValue: string, item: any, tabIndex: number, rowIndex: number) {
    if (sizeValue) {
      // 查找选中的尺码选项
      const selectedSize = this.sizeOptions.find((size) => size.value === sizeValue);
      if (selectedSize) {
        // 更新尺码信息
        item.size = selectedSize.label;
        item.originalLine.size_info = {
          spec_id: selectedSize.value,
          spec_size: selectedSize.label,
          spec_code: selectedSize.code || '',
        };

        // 检查并删除重复的颜色+尺码组合
        this.removeDuplicateColorSizeCombinations(tabIndex, item.originalLine, rowIndex);

        // 重新计算所有行的indexing
        this.recalculateIndexing(tabIndex);

        // 更新change_po_lines中的数据
        this.updateChangePoLines(tabIndex);

        // 触发数据变更事件
        this.triggerTableDataChange('updateColumn', tabIndex, [item.originalLine]);
      }
    } else {
      // 清空选择
      item.size = '';
      item.originalLine.size_info = {
        spec_id: null,
        spec_size: '',
        spec_code: '',
      };

      // 更新change_po_lines中的数据
      this.updateChangePoLines(tabIndex);

      // 触发数据变更事件
      this.triggerTableDataChange('removeColumn', tabIndex, [item.originalLine]);
    }
  }

  /**
   * 检查并删除重复的颜色+尺码组合
   * @param tabIndex tab索引
   * @param currentLine 当前行数据
   * @param currentRowIndex 当前行索引
   */
  private removeDuplicateColorSizeCombinations(tabIndex: number, currentLine: any, currentRowIndex: number) {
    const currentTabData = this.detail.pos[tabIndex];
    if (!currentTabData.change_po_lines) {
      return;
    }

    const currentColorId = currentLine.color_info.color_id;
    const currentColorName = currentLine.color_info.color_name;
    const currentSizeId = currentLine.size_info.spec_id;

    // 如果当前行没有颜色或尺码信息，不进行重复检查
    if (!currentColorId || !currentSizeId) {
      return;
    }

    // 查找所有重复的颜色+尺码组合
    const duplicateIndices: number[] = [];
    const processedData = this.processedData[tabIndex] || [];

    processedData.forEach((item: any, index: number) => {
      // 跳过当前行
      if (index === currentRowIndex) {
        return;
      }

      const itemColorId = item.originalLine.color_info.color_id;
      const itemSizeId = item.originalLine.size_info.spec_id;

      // 检查是否有相同的颜色+尺码组合
      if (itemColorId === currentColorId && itemSizeId === currentSizeId) {
        duplicateIndices.push(index);
      }
    });

    // 如果找到重复项，删除它们
    if (duplicateIndices.length > 0) {
      // 🔥 关键修复：通过originalLine引用找到在change_po_lines中的正确索引
      const removedRows: any[] = [];
      const changePoLinesToRemove: any[] = [];

      duplicateIndices.forEach((processedIndex) => {
        const processedItem = processedData[processedIndex];
        if (processedItem && processedItem.originalLine) {
          const originalLineToRemove = processedItem.originalLine;

          // 在change_po_lines中找到对应的索引
          const actualIndex = currentTabData.change_po_lines.findIndex((line: any) => line === originalLineToRemove);

          if (actualIndex !== -1) {
            const removedRow = currentTabData.change_po_lines[actualIndex];
            removedRows.push(removedRow);
            changePoLinesToRemove.push({ index: actualIndex, row: removedRow });
          }
        }
      });

      // 按索引从大到小排序，避免删除时索引变化影响
      changePoLinesToRemove.sort((a, b) => b.index - a.index);

      // 执行删除操作
      changePoLinesToRemove.forEach(({ index, row }) => {
        // 从change_po_lines中删除
        currentTabData.change_po_lines.splice(index, 1);

        // 如果是已保存的行，添加到删除列表
        if (row.id > 0) {
          if (!currentTabData.delete_po_lines) {
            currentTabData.delete_po_lines = [];
          }
          currentTabData.delete_po_lines.push(row);
        }
      });

      // 更新processedData
      this.updateProcessedData(tabIndex);

      // 触发数据变更事件
      if (removedRows.length > 0) {
        this.triggerTableDataChange('removeDuplicates', tabIndex, removedRows);
      }
    }
  }

  /**
   * 数量变更事件 - 参考flc-color-size-table的onQtyChange方法
   * @param qty 新的数量值
   * @param item 当前行数据
   * @param tabIndex tab索引
   * @param rowIndex 行索引
   */
  onQtyChange(qty: number, item: any, tabIndex: number, rowIndex: number) {
    // 更新数量
    item.qty = qty;
    item.originalLine.qty = qty;

    // 重新计算相关数据
    this.calculateItemTotals(item);
    this.calculateOverflowAndShortage(item);

    // 同步计算后的数据到原始数据
    item.originalLine.sku_amount = String(item.sku_amount || 0);
    item.originalLine.excess_quantity = item.excess_quantity;
    item.originalLine.deficiency_quantity = item.deficiency_quantity;

    // 重新计算合计数据
    this.calculateTabTotals(tabIndex);

    // 重新计算预收款
    this.calculateUpfrontPayment();

    // 只更新内存中的数据，不触发表格重新渲染
    // 数据同步将在失去焦点或保存时进行
  }

  /**
   * 单价变更事件 - 同步更新相同颜色的所有行
   * @param item 当前行数据
   * @param tabIndex tab索引
   */
  onUnitPriceChange(item: any, tabIndex: number) {
    const color = item.color;
    const unitPrice = item.unit_price;

    // 更新相同颜色的所有行的单价
    this.processedData[tabIndex].forEach((row: any) => {
      if (row.color === color) {
        row.unit_price = unitPrice;
        row.originalLine.unit_price = unitPrice;
        this.calculateItemTotals(row);
        // 同步计算后的金额到原始数据
        row.originalLine.sku_amount = String(row.sku_amount || 0);
      }
    });

    // 重新计算合计
    this.calculateTabTotals(tabIndex);

    // 重新计算预收款
    this.calculateUpfrontPayment();

    // 只更新内存中的数据，不触发表格重新渲染
    // 数据同步将在失去焦点或保存时进行
  }

  /**
   * 溢装比例变更事件
   * @param item 当前行数据
   * @param tabIndex tab索引
   */
  onOverflowRatioChange(item: any, tabIndex: number) {
    // 更新原始数据 - 确保转换为字符串
    item.originalLine.excess_rate = String(item.excess_rate || '');

    // 重新计算溢装数量
    this.calculateOverflowAndShortage(item);

    // 同步溢装数量到原始数据
    item.originalLine.excess_quantity = item.excess_quantity;

    // 重新计算合计
    this.calculateTabTotals(tabIndex);

    // 只更新内存中的数据，不触发表格重新渲染
    // 数据同步将在失去焦点或保存时进行
  }

  /**
   * 短装比例变更事件
   * @param item 当前行数据
   * @param tabIndex tab索引
   */
  onShortageRatioChange(item: any, tabIndex: number) {
    // 更新原始数据 - 确保转换为字符串
    item.originalLine.deficiency_rate = String(item.deficiency_rate || '');

    // 重新计算短装数量
    this.calculateOverflowAndShortage(item);

    // 同步短装数量到原始数据
    item.originalLine.deficiency_quantity = item.deficiency_quantity;

    // 重新计算合计
    this.calculateTabTotals(tabIndex);

    // 只更新内存中的数据，不触发表格重新渲染
    // 数据同步将在失去焦点或保存时进行
  }

  /**
   * 获取nz-table的数据，模拟flc-color-size-table的getCellDataList方法
   * @param tabIndex tab索引
   * @returns 表格数据数组
   */
  getNzTableDataList(tabIndex: number): any[] {
    const processedData = this.processedData[tabIndex] || [];
    return processedData.map((item: any) => item.originalLine);
  }

  /**
   * 删除指定行
   * @param tabIndex tab索引
   * @param rowIndex 行索引（processedData中的索引）
   */
  deleteRow(tabIndex: number, rowIndex: number) {
    const currentTabData = this.detail.pos[tabIndex];
    const processedDataForTab = this.processedData[tabIndex];

    if (!currentTabData.change_po_lines || !processedDataForTab || rowIndex >= processedDataForTab.length) {
      return;
    }

    // 🔥 关键修复：从processedData中获取要删除的行，然后找到其在change_po_lines中的正确索引
    const processedRowToDelete = processedDataForTab[rowIndex];
    const originalLineToDelete = processedRowToDelete.originalLine;

    // 在change_po_lines中找到对应的索引
    const actualRowIndex = currentTabData.change_po_lines.findIndex((line: any) => line === originalLineToDelete);

    if (actualRowIndex === -1) {
      console.error('无法找到要删除的行在change_po_lines中的索引');
      return;
    }

    // 获取要删除的行数据
    const removedRow = currentTabData.change_po_lines[actualRowIndex];

    // 从change_po_lines中删除行
    currentTabData.change_po_lines.splice(actualRowIndex, 1);

    // 添加到删除列表（如果是已保存的行）
    if (removedRow.id > 0) {
      if (!currentTabData.delete_po_lines) {
        currentTabData.delete_po_lines = [];
      }
      currentTabData.delete_po_lines.push(removedRow);
    }

    // 重新计算所有行的indexing
    this.recalculateIndexing(tabIndex);

    // 更新processedData
    this.updateProcessedData(tabIndex);

    // 触发数据变更事件
    this.triggerTableDataChange('removeRow', tabIndex, [removedRow]);
  }

  /**
   * 输入框失去焦点时同步数据
   * @param tabIndex tab索引
   * @param item 当前行数据
   */
  onInputBlur(tabIndex: number, item: any) {
    // 更新change_po_lines中的数据
    this.updateChangePoLines(tabIndex);

    // 触发数据变更事件
    this.triggerTableDataChange('updateCell', tabIndex, [item.originalLine]);
  }

  /**
   * 确保指定 tab 有至少一个空行，如果没有数据则创建一个空行
   * @param tabIndex tab索引
   */
  private ensureEmptyRowExists(tabIndex: number) {
    const currentTabData = this.detail.pos[tabIndex];

    // 确保 change_po_lines 存在
    if (!currentTabData.change_po_lines) {
      currentTabData.change_po_lines = [];
    }

    // 如果没有数据，创建一个空行
    if (currentTabData.change_po_lines.length === 0) {
      // 获取当前tab的最大indexing值
      const maxIndexing = currentTabData.maxIndexing || 0;

      // 创建新的空行数据
      const newRowData = {
        id: 0,
        line_uuid: '',
        indexing: maxIndexing + 1,
        deletable: true,
        changeable: true,
        qty: 0,
        unit_price: 0,
        excess_rate: 0,
        deficiency_rate: 0,
        excess_quantity: 0,
        deficiency_quantity: 0,
        color_info: {
          color_id: null,
          color_name: '',
          color_code: '',
        },
        size_info: {
          spec_id: null,
          spec_size: '',
          spec_code: '',
        },
      };

      // 添加空行到数据中
      currentTabData.change_po_lines.push(newRowData);

      // 更新maxIndexing
      currentTabData.maxIndexing = maxIndexing + 1;
    }
  }

  /**
   * 在表格最底部添加新的空行
   * @param tabIndex tab索引
   * @param rowIndex 行索引（忽略，始终添加到最底部）
   */
  addRow(tabIndex: number, rowIndex: number) {
    const currentTabData = this.detail.pos[tabIndex];
    if (!currentTabData.change_po_lines) {
      currentTabData.change_po_lines = [];
    }

    // 检查是否有未选择颜色的行
    const hasEmptyColorRow = currentTabData.change_po_lines.some((line: any) => !line.color_info.color_id && !line.color_info.color_name);

    if (hasEmptyColorRow) {
      this._msg.create('warning', '有未选择的颜色行，请先完善颜色信息');
      return;
    }

    // 始终添加到表格最底部
    const lastIndex = currentTabData.change_po_lines.length - 1;
    this.addRowAtPosition(tabIndex, lastIndex);
  }

  /**
   * 查找相同颜色组的最后一行索引
   * @param tabIndex tab索引
   * @param colorId 颜色ID
   * @param colorName 颜色名称
   * @returns 最后一行的索引
   */
  private findLastRowIndexOfSameColor(tabIndex: number, colorId: any, colorName: string): number {
    const processedData = this.processedData[tabIndex] || [];
    let lastIndex = -1;

    for (let i = 0; i < processedData.length; i++) {
      const item = processedData[i];
      if (item.originalLine.color_info.color_id === colorId && item.originalLine.color_info.color_name === colorName) {
        lastIndex = i;
      }
    }

    return lastIndex;
  }

  /**
   * 在指定位置添加新行
   * @param tabIndex tab索引
   * @param rowIndex 行索引
   */
  private addRowAtPosition(tabIndex: number, rowIndex: number) {
    const currentTabData = this.detail.pos[tabIndex];

    // 创建新的空行数据（暂时不设置indexing）
    const newRowData = {
      id: 0,
      line_uuid: '',
      indexing: 0, // 临时值，稍后重新计算
      deletable: true,
      changeable: true,
      qty: 0,
      unit_price: 0,
      excess_rate: 0,
      deficiency_rate: 0,
      excess_quantity: 0,
      deficiency_quantity: 0,
      color_info: {
        color_id: null,
        color_name: '',
        color_code: '',
      },
      size_info: {
        spec_id: null,
        spec_size: '',
        spec_code: '',
      },
    };

    // 在指定位置后插入新行
    currentTabData.change_po_lines.splice(rowIndex + 1, 0, newRowData);

    // 重新计算所有行的indexing
    this.recalculateIndexing(tabIndex);

    // 使用 setTimeout 确保 DOM 更新完成后再重新计算 rowspan
    setTimeout(() => {
      // 更新processedData
      this.updateProcessedData(tabIndex);

      // 触发数据变更事件
      this.triggerTableDataChange('addRow', tabIndex, [newRowData]);
    }, 0);
  }

  /**
   * 重新计算指定tab的所有行的indexing值
   * @param tabIndex tab索引
   */
  private async recalculateIndexing(tabIndex: number) {
    const currentTabData = this.detail.pos[tabIndex];
    if (!currentTabData.change_po_lines) {
      return;
    }

    try {
      // 获取尺码选项的标准顺序
      const sizeOptions = await this.onFetchSizeOptions('', true);
      const sizeOrderMap = new Map<number, number>(); // spec_id -> indexing
      let currentIndexing = 1;

      // 按照尺码选项的标准顺序为每个存在的尺码分配indexing
      sizeOptions.forEach((sizeOption: any) => {
        const specId = sizeOption.value;
        // 检查当前数据中是否存在这个尺码
        const hasThisSize = currentTabData.change_po_lines.some((line: any) => line.size_info?.spec_id === specId);

        if (hasThisSize) {
          sizeOrderMap.set(specId, currentIndexing++);
        }
      });

      // 为所有行分配indexing值
      currentTabData.change_po_lines.forEach((line: any) => {
        const specId = line.size_info?.spec_id;
        if (specId && specId > 0 && sizeOrderMap.has(specId)) {
          line.indexing = sizeOrderMap.get(specId) || 0;
        } else {
          // 对于无效或未选择的尺码，使用递增的indexing
          line.indexing = currentIndexing++;
        }
      });

      // 更新maxIndexing
      currentTabData.maxIndexing = currentIndexing - 1;
    } catch (error) {
      // 如果获取尺码选项失败，使用简单的按出现顺序分配
      this.recalculateIndexingByOrder(tabIndex);
    }
  }

  /**
   * 按数据出现顺序重新计算indexing（备用方法）
   * @param tabIndex tab索引
   */
  private recalculateIndexingByOrder(tabIndex: number) {
    const currentTabData = this.detail.pos[tabIndex];
    if (!currentTabData.change_po_lines) {
      return;
    }

    const sizeOrderMap = new Map<number, number>(); // spec_id -> indexing
    let currentIndexing = 1;

    // 按照数据中出现的顺序为每个唯一的尺码分配indexing
    currentTabData.change_po_lines.forEach((line: any) => {
      const specId = line.size_info?.spec_id;
      if (specId && specId > 0) {
        if (!sizeOrderMap.has(specId)) {
          sizeOrderMap.set(specId, currentIndexing++);
        }
      }
    });

    // 为所有行分配indexing值
    currentTabData.change_po_lines.forEach((line: any) => {
      const specId = line.size_info?.spec_id;
      if (specId && specId > 0) {
        line.indexing = sizeOrderMap.get(specId) || 0;
      } else {
        line.indexing = currentIndexing++;
      }
    });

    // 更新maxIndexing
    currentTabData.maxIndexing = currentIndexing - 1;
  }

  /**
   * 重新计算加载数据的indexing值
   * @param tabData 交付单数据
   */
  private recalculateIndexingForLoadedData(tabData: any) {
    if (!tabData.po_lines) {
      return;
    }

    // 按尺码分组，收集所有唯一的尺码
    const sizeMap = new Map<number, number>(); // spec_id -> indexing
    let currentIndexing = 1;

    // 遍历所有行，为每个唯一的尺码分配indexing
    tabData.po_lines.forEach((line: any) => {
      const specId = line.size_info?.spec_id;
      if (specId && specId > 0) {
        // 只处理有效的尺码
        if (!sizeMap.has(specId)) {
          sizeMap.set(specId, currentIndexing++);
        }
      }
    });

    // 为所有行分配indexing值
    tabData.po_lines.forEach((line: any) => {
      const specId = line.size_info?.spec_id;
      if (specId && specId > 0) {
        line.indexing = sizeMap.get(specId) || 0;
      } else {
        // 对于无效或未选择的尺码，使用最大indexing + 1
        line.indexing = currentIndexing++;
      }
    });
  }

  /**
   * 复制指定行 - 直接在当前行后复制
   * @param tabIndex tab索引
   * @param rowIndex 行索引（processedData中的索引）
   */
  copyRow(tabIndex: number, rowIndex: number) {
    const currentTabData = this.detail.pos[tabIndex];
    const processedDataForTab = this.processedData[tabIndex];

    if (!currentTabData.change_po_lines || !processedDataForTab || rowIndex >= processedDataForTab.length) {
      return;
    }

    // 🔥 关键修复：从processedData中获取要复制的行，然后找到其在change_po_lines中的正确索引
    const processedRowToCopy = processedDataForTab[rowIndex];
    const originalLineToCopy = processedRowToCopy.originalLine;

    // 在change_po_lines中找到对应的索引
    const actualRowIndex = currentTabData.change_po_lines.findIndex((line: any) => line === originalLineToCopy);

    if (actualRowIndex === -1) {
      console.error('无法找到要复制的行在change_po_lines中的索引');
      return;
    }

    // 获取要复制的行数据
    const sourceRow = currentTabData.change_po_lines[actualRowIndex];

    // 创建复制的行数据（暂时不设置indexing）
    const copiedRowData = {
      ...JSON.parse(JSON.stringify(sourceRow)),
      id: 0,
      line_uuid: '',
      indexing: 0, // 临时值，稍后重新计算
      deletable: true,
      changeable: true,
      // 🔥 复制时清空尺码信息，让用户重新选择
      size_info: {
        spec_id: null,
        spec_size: '',
        spec_code: '',
      },
    };

    // 直接在当前行后插入复制的行
    currentTabData.change_po_lines.splice(actualRowIndex + 1, 0, copiedRowData);

    // 重新计算所有行的indexing
    this.recalculateIndexing(tabIndex);

    // 更新processedData
    this.updateProcessedData(tabIndex);

    // 触发数据变更事件
    this.triggerTableDataChange('copyRow', tabIndex, [copiedRowData]);
  }

  /**
   * 获取所有nz-table的数据
   * @returns 所有tab的表格数据
   */
  getAllNzTableData(): any[][] {
    return this.processedData.map((_, index: number) => {
      return this.getNzTableDataList(index);
    });
  }

  /**
   * 验证nz-table数据的有效性，模拟flc-color-size-table的validate方法
   * @returns 是否有效
   */
  validateNzTableData(): boolean {
    return this.processedData.every((tabData: any[]) => {
      return tabData.every((item: any) => {
        return item.color && item.size && item.qty !== null && item.unit_price !== null;
      });
    });
  }

  /**
   * 触发表格数据变更事件，模拟flc-color-size-table的onChange事件
   * @param eventType 事件类型
   * @param tabIndex tab索引
   * @param affectedData 受影响的数据
   */
  private triggerTableDataChange(eventType: string, tabIndex: number, affectedData: any[] = []) {
    // 获取当前tab的所有数据
    const currentTabData = this.detail.pos[tabIndex];
    if (!currentTabData) return;

    // 构造类似flc-color-size-table的onChange事件数据
    const changeEvent = {
      eventType: eventType,
      tableData: currentTabData.change_po_lines || [],
      affectedData: affectedData,
    };

    // 调用原有的sizeColorChange方法来处理数据变更
    this.sizeColorChange(changeEvent);
  }

  /**
   * 根据值查找颜色选项
   */
  private findColorByValue(value: string, options: any[]): any {
    for (const option of options) {
      if (option.value === value) {
        return option;
      }
      if (option.children && option.children.length > 0) {
        const found = this.findColorByValue(value, option.children);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }

  /**
   * 获取该tab下的表格数据
   */
  sizeColorChange(e: any) {
    const maxIndexing =
      e.tableData.length > 0
        ? e.tableData.reduce((prev: any, current: any) => (prev.indexing > current.indexing ? prev : current))?.indexing
        : 0;

    this.detail.pos[this.tabIndex]['change_po_lines'] = e.tableData || [];
    this.detail.pos[this.tabIndex]['maxIndexing'] = maxIndexing;

    if (e.eventType === 'removeColumn' || e.eventType === 'removeRow') {
      if (!this.detail.pos[this.tabIndex]['delete_po_lines']) {
        this.detail.pos[this.tabIndex]['delete_po_lines'] = [];
      }
      this.detail.pos[this.tabIndex]['delete_po_lines'] = [...this.detail.pos[this.tabIndex]['delete_po_lines'], ...(e.affectedData || [])];
    }

    // 更新当前tab的交付单表格数据
    this.updateProcessedData(this.tabIndex);
    this.sumTotal();
  }

  // 更新指定tab的处理后数据
  private updateProcessedData(tabIndex: number) {
    const poLines = this.detail.pos[tabIndex]?.change_po_lines || [];
    const existingProcessedData = this.processedData[tabIndex] || [];

    // 创建现有数据的映射，用于保留用户修改的值和颜色出现顺序
    const existingDataMap = new Map<string, any>();
    const existingColorOrderMap = new Map<string, number>(); // 保存颜色的原始出现顺序

    existingProcessedData.forEach((item: any) => {
      const key = `${item.originalLine.color_info.color_id || 'null'}-${item.originalLine.size_info.spec_id || 'null'}`;
      const colorKey = `${item.originalLine.color_info.color_id || 'null'}-${item.originalLine.color_info.color_name || ''}`;

      existingDataMap.set(key, {
        unit_price: item.unit_price,
        excess_rate: item.excess_rate,
        deficiency_rate: item.deficiency_rate,
        qty: item.qty,
      });

      // 保存颜色的原始出现顺序
      if (!existingColorOrderMap.has(colorKey) && item.colorFirstAppearanceIndex !== undefined) {
        existingColorOrderMap.set(colorKey, item.colorFirstAppearanceIndex);
      }
    });

    // 🔥 关键修复：为新颜色分配出现顺序索引
    let maxColorOrder = Math.max(...Array.from(existingColorOrderMap.values()), -1);
    const newColorOrderMap = new Map<string, number>();

    poLines.forEach((line: any, index: number) => {
      const colorKey = `${line.color_info.color_id || 'null'}-${line.color_info.color_name || ''}`;

      if (!existingColorOrderMap.has(colorKey) && !newColorOrderMap.has(colorKey)) {
        // 为新颜色分配顺序索引
        newColorOrderMap.set(colorKey, ++maxColorOrder);
      }
    });

    // 转换数据格式，保留用户已修改的值
    const processedData = poLines.map((line: any, index: number) => {
      const key = `${line.color_info.color_id || 'null'}-${line.size_info.spec_id || 'null'}`;
      const colorKey = `${line.color_info.color_id || 'null'}-${line.color_info.color_name || ''}`;
      const existingData = existingDataMap.get(key);

      // 获取颜色的首次出现顺序
      const colorFirstAppearanceIndex = existingColorOrderMap.get(colorKey) ?? newColorOrderMap.get(colorKey) ?? index;

      return {
        color: line.color_info.color_name,
        size: line.size_info.spec_size,
        qty: existingData?.qty !== undefined ? existingData.qty : line.qty || 0,
        unit_price: existingData?.unit_price !== undefined ? existingData.unit_price : Number(line.unit_price || 0),
        sku_amount: 0,
        excess_rate: existingData?.excess_rate !== undefined ? existingData.excess_rate : Number(line.excess_rate || 0),
        excess_quantity: line.excess_quantity || 0,
        deficiency_rate: existingData?.deficiency_rate !== undefined ? existingData.deficiency_rate : Number(line.deficiency_rate || 0),
        deficiency_quantity: line.deficiency_quantity || 0,
        colorRowspan: 0,
        unitPriceRowspan: 0,
        // 保存原始数据的引用，用于同步更新
        originalLine: line,
        originalIndex: index, // 当前在change_po_lines中的索引
        colorFirstAppearanceIndex: colorFirstAppearanceIndex, // 🔥 保存颜色的首次出现顺序
      };
    });

    // 🔥 关键修复：按颜色分组排序，确保相同颜色的行连续，但保持原有颜色顺序
    processedData.sort((a: any, b: any) => {
      // 空行排到最后
      const aIsEmpty = !a.originalLine.color_info.color_id && !a.originalLine.color_info.color_name;
      const bIsEmpty = !b.originalLine.color_info.color_id && !b.originalLine.color_info.color_name;

      if (aIsEmpty && !bIsEmpty) return 1;
      if (!aIsEmpty && bIsEmpty) return -1;
      if (aIsEmpty && bIsEmpty) return a.originalIndex - b.originalIndex;

      // 按颜色分组，使用保存的颜色首次出现顺序
      if (a.colorFirstAppearanceIndex !== b.colorFirstAppearanceIndex) {
        return a.colorFirstAppearanceIndex - b.colorFirstAppearanceIndex;
      }

      // 相同颜色内部按尺码ID排序（与processDataForTable保持一致）
      return a.originalLine.size_info.spec_id - b.originalLine.size_info.spec_id;
    });

    // 安全地计算 rowspan，确保数据顺序一致性和表格结构完整
    // 重置所有 rowspan
    processedData.forEach((item: any) => {
      item.colorRowspan = 0;
      item.unitPriceRowspan = 0;
    });

    // 按颜色分组，但保持原始数据顺序
    const colorGroups: { [key: string]: any[] } = {};
    const emptyRows: any[] = [];

    // 收集颜色组和空行
    processedData.forEach((item: any) => {
      const isEmpty = !item.originalLine.color_info.color_id && !item.originalLine.color_info.color_name;

      if (isEmpty) {
        // 空行单独处理
        emptyRows.push(item);
      } else {
        // 有颜色信息的行按颜色分组
        const colorKey = `${item.originalLine.color_info.color_id || 'null'}-${item.originalLine.color_info.color_name || ''}`;

        if (!colorGroups[colorKey]) {
          colorGroups[colorKey] = [];
        }
        colorGroups[colorKey].push(item);
      }
    });

    // 处理有颜色信息的行：为每个颜色组设置 rowspan
    Object.keys(colorGroups).forEach((colorKey) => {
      const items = colorGroups[colorKey];
      const colorRowCount = items.length;

      // 找到该颜色组在原始数据中的第一行
      let firstRowIndex = -1;
      for (let i = 0; i < processedData.length; i++) {
        const item = processedData[i];
        const itemColorKey = `${item.originalLine.color_info.color_id || 'null'}-${item.originalLine.color_info.color_name || ''}`;
        if (itemColorKey === colorKey) {
          firstRowIndex = i;
          break;
        }
      }

      // 设置 rowspan：只有第一次出现的行设置 rowspan，其他行设置为 0
      items.forEach((item) => {
        const itemIndex = processedData.indexOf(item);
        if (itemIndex === firstRowIndex) {
          // 第一行设置 rowspan
          item.colorRowspan = colorRowCount;
          item.unitPriceRowspan = colorRowCount;
        } else {
          // 其他行设置为 0
          item.colorRowspan = 0;
          item.unitPriceRowspan = 0;
        }
      });
    });

    // 处理空行：每个空行都设置为1，确保表格结构完整
    emptyRows.forEach((item) => {
      item.colorRowspan = 1;
      item.unitPriceRowspan = 1;
    });

    // 为所有行计算金额和数量
    processedData.forEach((item: any) => {
      this.calculateItemTotals(item);
      this.calculateOverflowAndShortage(item);
      // 同步计算后的数据到原始数据
      item.originalLine.sku_amount = String(item.sku_amount || 0);
      item.originalLine.excess_quantity = item.excess_quantity;
      item.originalLine.deficiency_quantity = item.deficiency_quantity;
    });

    // 更新对应tab的数据
    this.processedData[tabIndex] = processedData;

    // 调试：验证 rowspan 正确性
    if (typeof window !== 'undefined' && (window as any).location?.hostname === 'localhost') {
      this.validateRowspanStructure(tabIndex);
    }

    this.calculateTabTotals(tabIndex);
    this.processMergedTableData();
  }

  /**
   * 验证 rowspan 结构的正确性
   * @param tabIndex tab索引
   */
  private validateRowspanStructure(tabIndex: number) {
    // 移除所有调试输出，保持方法结构但不输出日志
  }

  // 计算指定tab的合计数据
  private calculateTabTotals(tabIndex: number) {
    if (!this.processedData[tabIndex]) {
      this.tableTotals[tabIndex] = {
        qty: 0,
        unit_price: 0,
        sku_amount: 0,
        excess_quantity: 0,
        deficiency_quantity: 0,
      };
    } else {
      this.tableTotals[tabIndex] = this.processedData[tabIndex].reduce(
        (acc: any, curr: any) => ({
          qty: acc.qty + (curr.qty || 0),
          unit_price: 0,
          sku_amount: acc.sku_amount + (curr.sku_amount || 0),
          excess_quantity: acc.excess_quantity + (curr.excess_quantity || 0),
          deficiency_quantity: acc.deficiency_quantity + (curr.deficiency_quantity || 0),
        }),
        {
          qty: 0,
          unit_price: 0,
          sku_amount: 0,
          excess_quantity: 0,
          deficiency_quantity: 0,
        }
      );
    }

    // 更新合计表格数据
    this.processMergedTableData();
  }

  // 处理合并后的表格数据
  private processMergedTableData() {
    // 合并所有tab的数据，同时记录交付单信息
    const allData: any[] = [];
    this.processedData.forEach((tabData: any[], tabIndex: number) => {
      if (tabData) {
        tabData.forEach((item: any) => {
          allData.push({
            ...item,
            tabIndex: tabIndex, // 记录交付单索引
            poCode: this.detail.pos[tabIndex]?.po_basic?.po_code || `交付单${tabIndex + 1}`, // 记录交付单编码
          });
        });
      }
    });

    // 🔥 修复：按颜色+尺码分组，但要考虑全局颜色首次出现顺序
    const groupedData = new Map<string, any>();
    const globalColorFirstAppearance = new Map<string, number>(); // 全局颜色首次出现顺序

    // 首先收集全局颜色首次出现顺序
    let globalColorIndex = 0;
    allData.forEach((item) => {
      const colorKey = item.color || '';
      if (colorKey && !globalColorFirstAppearance.has(colorKey)) {
        globalColorFirstAppearance.set(colorKey, globalColorIndex++);
      }
    });

    // 按颜色+尺码分组合并数据
    allData.forEach((item) => {
      const key = `${item.color}-${item.size}`;
      if (!groupedData.has(key)) {
        groupedData.set(key, {
          color: item.color,
          size: item.size,
          qty: 0,
          sku_amount: 0,
          excess_quantity: 0,
          deficiency_quantity: 0,
          colorRowspan: 0,
          // 记录第一次出现的交付单信息，用于排序
          firstTabIndex: item.tabIndex,
          firstPoCode: item.poCode,
          // 🔥 使用全局颜色首次出现顺序
          globalColorFirstAppearance: globalColorFirstAppearance.get(item.color || '') || 0,
        });
      }
      const group = groupedData.get(key);
      group.qty += item.qty || 0;
      group.sku_amount += item.sku_amount || 0;
      group.excess_quantity += item.excess_quantity || 0;
      group.deficiency_quantity += item.deficiency_quantity || 0;
    });

    // 转换为数组并按交付单+颜色排序
    this.mergedTableData = Array.from(groupedData.values());

    // 🔥 修复：按全局颜色首次出现顺序排序，相同颜色的不同尺码按尺码ID排序
    this.mergedTableData.sort((a, b) => {
      // 首先按全局颜色首次出现顺序排序
      if (a.globalColorFirstAppearance !== b.globalColorFirstAppearance) {
        return a.globalColorFirstAppearance - b.globalColorFirstAppearance;
      }

      // 相同颜色内按尺码ID排序（确保尺码顺序正确）
      const aSizeId = this.getSizeIdFromMergedItem(a);
      const bSizeId = this.getSizeIdFromMergedItem(b);
      if (aSizeId !== bSizeId) {
        return aSizeId - bSizeId;
      }

      // 最后按颜色名称排序（作为备用排序）
      return a.color.localeCompare(b.color);
    });

    // 🔥 修复：重新计算colorRowspan，考虑跨交付单的情况
    this.calculateMergedTableColorRowspan();

    // 计算总计
    this.mergedTotals = this.mergedTableData.reduce(
      (acc, curr) => ({
        qty: acc.qty + (curr.qty || 0),
        sku_amount: acc.sku_amount + (curr.sku_amount || 0),
        excess_quantity: acc.excess_quantity + (curr.excess_quantity || 0),
        deficiency_quantity: acc.deficiency_quantity + (curr.deficiency_quantity || 0),
      }),
      {
        qty: 0,
        sku_amount: 0,
        excess_quantity: 0,
        deficiency_quantity: 0,
      }
    );
  }

  /**
   * 🔥 获取合并表格项的尺码ID（用于排序）
   * @param item 合并表格项
   * @returns 尺码ID
   */
  private getSizeIdFromMergedItem(item: any): number {
    // 尝试从原始数据中获取尺码ID
    // 由于合并表格数据只保存了尺码名称，我们需要通过尺码选项查找ID
    const sizeOption = this.sizeOptions?.find((option) => option.label === item.size);
    return sizeOption?.value || 999999; // 未找到的尺码排到最后
  }

  /**
   * 🔥 计算合并表格的颜色rowspan，相同颜色合并展示
   */
  private calculateMergedTableColorRowspan() {
    // 重置所有rowspan
    this.mergedTableData.forEach((item) => {
      item.colorRowspan = 0;
    });

    // 🔥 关键修复：按颜色分组计算rowspan，相同颜色的所有尺码合并显示
    let currentColor = '';
    let colorCount = 0;
    let colorStartIndex = 0;

    this.mergedTableData.forEach((item, index) => {
      const itemColor = item.color || '';

      // 检查是否是新的颜色
      if (itemColor !== currentColor) {
        // 为上一个颜色组设置rowspan
        if (currentColor !== '' && colorCount > 0) {
          this.mergedTableData[colorStartIndex].colorRowspan = colorCount;
        }

        // 开始新的颜色组
        currentColor = itemColor;
        colorStartIndex = index;
        colorCount = 1;
      } else {
        // 同一颜色组，增加计数
        colorCount++;
      }

      // 处理最后一个颜色组
      if (index === this.mergedTableData.length - 1 && colorCount > 0) {
        this.mergedTableData[colorStartIndex].colorRowspan = colorCount;
      }
    });
  }

  /**
   * 合计拖拽排序
   */
  sumSizeColorChange(e: FlcColorSizeTableChangeEvent) {
    this.detail.io_lines = e.tableData || [];
  }
  /**
   * 合计
   */
  async sumTotal() {
    let data: any[] = [];
    this.detail.pos.forEach((item: any) => {
      data = JSON.parse(JSON.stringify([...data, ...(item?.change_po_lines || [])]));
    });
    const sumData: Array<FlcColorSizeTableCell> = [];

    data.forEach((d) => {
      const sameCellIdx = sumData.findIndex(
        (_d) =>
          Number(_d.size_info.spec_id) === Number(d.size_info.spec_id) && Number(_d.color_info.color_id) === Number(d.color_info.color_id)
      );
      if (sameCellIdx !== -1) {
        sumData[sameCellIdx].qty = d.qty + (sumData[sameCellIdx].qty ?? 0);
      } else {
        // 暂时不设置indexing，稍后统一计算
        d.indexing = 0;
        sumData.push(d);
      }
    });

    // 使用与recalculateIndexing相同的逻辑计算indexing
    await this.calculateIoLinesIndexing(sumData);

    this.detail.io_lines = sumData;
    // setTimeout(() => {
    //   if (this.colorSizeTableSum?.getTotalSum() > 100000000) {
    //     this.notification.create('error', this._service.translateValue(this.translateName + 'IO合计总数量不能超过100000000请修改'), '');
    //   }
    // });
  }

  /**
   * 计算io_lines的indexing值
   * @param sumData io_lines数据
   */
  private async calculateIoLinesIndexing(sumData: any[]) {
    try {
      // 获取尺码选项的标准顺序
      const sizeOptions = await this.onFetchSizeOptions('', true);
      const sizeOrderMap = new Map<number, number>(); // spec_id -> indexing
      let currentIndexing = 1;

      // 按照尺码选项的标准顺序为每个存在的尺码分配indexing
      sizeOptions.forEach((sizeOption: any) => {
        const specId = sizeOption.value;
        // 检查sumData中是否存在这个尺码
        const hasThisSize = sumData.some((line: any) => line.size_info?.spec_id === specId);

        if (hasThisSize) {
          sizeOrderMap.set(specId, currentIndexing++);
        }
      });

      // 为所有行分配indexing值
      sumData.forEach((line: any) => {
        const specId = line.size_info?.spec_id;
        if (specId && specId > 0 && sizeOrderMap.has(specId)) {
          line.indexing = sizeOrderMap.get(specId) || 0;
        } else {
          // 对于无效或未选择的尺码，使用递增的indexing
          line.indexing = currentIndexing++;
        }
      });
    } catch (error) {
      // 如果获取尺码选项失败，使用简单的按出现顺序分配
      this.calculateIoLinesIndexingByOrder(sumData);
    }
  }

  /**
   * 按数据出现顺序计算io_lines的indexing（备用方法）
   * @param sumData io_lines数据
   */
  private calculateIoLinesIndexingByOrder(sumData: any[]) {
    const sizeOrderMap = new Map<number, number>(); // spec_id -> indexing
    let currentIndexing = 1;

    // 按照数据中出现的顺序为每个唯一的尺码分配indexing
    sumData.forEach((line: any) => {
      const specId = line.size_info?.spec_id;
      if (specId && specId > 0) {
        if (!sizeOrderMap.has(specId)) {
          sizeOrderMap.set(specId, currentIndexing++);
        }
      }
    });

    // 为所有行分配indexing值
    sumData.forEach((line: any) => {
      const specId = line.size_info?.spec_id;
      if (specId && specId > 0) {
        line.indexing = sizeOrderMap.get(specId) || 0;
      } else {
        line.indexing = currentIndexing++;
      }
    });
  }
  /**
   * 打开选择交付单
   */
  poSelectOpen(e: any) {
    if (e) {
      this.poSelectList = [];
      this.detail.pos.forEach((item: any, i: number) => {
        if (item.po_basic.po_code) {
          this.poSelectList.push({ label: item.po_basic.po_code, value: item.po_basic.po_code, tabIndex: i });
        }
      });
    }
  }
  /**
   * 选择交付单变化
   */
  poSelectChange(e: any) {
    if (e) {
      this.tabIndex = this.poSelectList.find((item: any) => {
        return item.value === e;
      }).tabIndex;
    } else {
      this.tabIndex = 0;
    }
  }
  /**
   * 取消
   */
  cancel() {
    if (this.id === 'new') {
      this._router.navigate(['/order/bulk/list']);
    } else {
      if (this.edit && (this.bulkForm.dirty || JSON.stringify(this.originDetail) !== JSON.stringify(this.detail))) {
        const ref = this._flcModalService.confirmCancel({
          type: 'confirm-cancel',
          okText: this._service.translateValue('flss.btn.ok'),
          cancelText: this._service.translateValue('flss.btn.cancel'),
        });
        ref.afterClose.subscribe((res: any) => {
          if (res) {
            this.edit = false;
            this.getDetail(this.id);
          }
        });
      } else {
        this.edit = false;
        this.getDetail(this.id);
      }
    }
  }
  /**
   * 处理上送data
   */
  handleValue() {
    // 在处理前，确保所有tab的change_po_lines都是最新的
    this.processedData.forEach((tabData: any[], index: number) => {
      if (tabData && tabData.length > 0) {
        this.updateChangePoLines(index);
      }
    });

    const hasEmpty = this.processedData.some((tabData: any[]) => {
      return tabData.some((item: any) => item.color == '' || item.size == '' || item.qty === '' || item.unit_price == '');
    });
    if (hasEmpty) {
      this._msg.warning('交付单颜色、尺码、件数、单价不能为空!');
      return;
    }

    // 同步processedData到pos
    this.processedData.forEach((tabData: any[], index: number) => {
      const currentPos = this.detail.pos[index];
      if (!tabData || !currentPos) return;

      // 合并更新逻辑
      const updatePoLine = (line: any, matchedData: any) => {
        const updates = {
          unit_price: String(matchedData.unit_price),
          sku_amount: String(matchedData.sku_amount),
          excess_rate: String(matchedData.excess_rate),
          excess_quantity: matchedData.excess_quantity || 0,
          deficiency_rate: String(matchedData.deficiency_rate),
          deficiency_quantity: matchedData.deficiency_quantity || 0,
          qty: matchedData.qty || 0,
        };

        // 更新 po_line
        Object.assign(line, updates);

        // 更新 change_po_lines
        currentPos.change_po_lines = currentPos.change_po_lines.map((c: any) =>
          c.color_info.color_id === line.color_info.color_id && c.size_info.spec_id === line.size_info.spec_id ? { ...c, ...updates } : c
        );
      };

      // 优化匹配和更新流程
      currentPos.po_lines = (currentPos.po_lines || []).map((line: any) => {
        const matchedData = tabData.find((item) => item.color === line.color_info.color_name && item.size === line.size_info.spec_size);
        return matchedData ? (updatePoLine(line, matchedData), line) : line;
      });
    });

    // 同步mergedTableData到io_lines
    if (this.mergedTableData && this.detail.io_lines) {
      const updatedIoLines = [...this.detail.io_lines];
      updatedIoLines.forEach((line: any) => {
        const matchedData = this.mergedTableData.find(
          (item) => item.color === line.color_info.color_name && item.size === line.size_info.spec_size
        );

        if (matchedData) {
          // 更新io_line的数据
          line.unit_price = String(matchedData.unit_price || '');
          line.sku_amount = String(matchedData.sku_amount || '');
          line.excess_rate = String(matchedData.excess_rate || '');
          line.excess_quantity = matchedData.excess_quantity || 0;
          line.deficiency_rate = String(matchedData.deficiency_rate || '');
          line.deficiency_quantity = matchedData.deficiency_quantity || 0;
          line.qty = matchedData.qty || 0;
        }
      });
      this.detail.io_lines = updatedIoLines;
    }

    const extraInfo = this.bulkForm.value.extra_process_info || [];
    delete this.bulkForm.value.style;
    delete this.bulkForm.value.extra_process_info;
    const extra_process_info: any[] = [];
    extraInfo.forEach((item: any) => {
      this.extraList.forEach((con: any) => {
        if (con.value === item) {
          let deletable = true;
          this.detail.io_basic.extra_process_info.forEach((kon: any) => {
            if (kon.extra_process_id == item) {
              deletable = kon.deletable;
            }
          });
          extra_process_info.push({
            extra_process_id: con.value,
            extra_process_name: con.label,
            position_list: [],
            deletable: deletable,
          });
        }
      });
    });
    let totalLines: any[] = [];
    this.detail.pos.forEach((item: any) => {
      const lines: any[] = [];
      item?.change_po_lines?.forEach((line: any) => {
        if ((line.qty ?? 0) > 0 && !lines.includes(`${line?.color_info?.color_id}${line?.color_info?.color_code}`)) {
          lines.push(`${line?.color_info?.color_id}${line?.color_info?.color_code}`);
        }
      });
      totalLines = [...totalLines, ...lines];
      item['delete_po_lines'] = [
        ...(item['delete_po_lines'] || []),
        ...(item?.change_po_lines ?? []).filter((item: any) => {
          return !lines.includes(`${item?.color_info?.color_id}${item?.color_info?.color_code}`);
        }),
      ];
    });
    const detailData = JSON.parse(JSON.stringify(this.detail));
    detailData.pos.forEach((item: any) => {
      // 确保 change_po_lines 中的数据类型正确
      if (item?.change_po_lines) {
        item.change_po_lines = item.change_po_lines.map((line: any) => ({
          ...line,
          unit_price: String(line.unit_price || 0),
          excess_rate: String(line.excess_rate || 0),
          deficiency_rate: String(line.deficiency_rate || 0),
        }));
      }

      const lines: any[] = [];
      item?.change_po_lines?.forEach((line: any) => {
        if ((line.qty ?? 0) > 0 && !lines.includes(`${line?.color_info?.color_id}${line?.color_info?.color_code}`)) {
          lines.push(`${line?.color_info?.color_id}${line?.color_info?.color_code}`);
        }
      });
      item['delete_po_lines'] = [
        ...(item['delete_po_lines'] || []),
        ...(item?.change_po_lines ?? []).filter((item: any) => {
          return !lines.includes(`${item?.color_info?.color_id}${item?.color_info?.color_code}`);
        }),
      ];

      // 过滤并确保 po_lines 的数据类型正确
      item['po_lines'] = item?.change_po_lines
        ?.filter((line: any) => {
          return lines.includes(`${line?.color_info?.color_id}${line?.color_info?.color_code}`);
        })
        .map((line: any) => ({
          ...line,
          unit_price: String(line.unit_price || 0),
          excess_rate: String(line.excess_rate || 0),
          deficiency_rate: String(line.deficiency_rate || 0),
        }));
      if (item.po_basic.due_time) {
        item.po_basic.due_time = format(startOfDay(new Date(item.po_basic.due_time)), 'T');
      }
      if (item.po_basic.customer_due_time) {
        item.po_basic.customer_due_time = format(startOfDay(new Date(item.po_basic.customer_due_time)), 'T');
      }
      if (item.po_basic.fob_price >= 0) {
        item.po_basic.fob_price = item.po_basic.fob_price?.toString();
      }
      if (item.po_basic.total_fob >= 0) {
        item.po_basic.total_fob = item.po_basic.total_fob?.toString();
      }
      if (item.po_basic.total_sales_cad >= 0) {
        item.po_basic.total_sales_cad = item.po_basic.total_sales_cad?.toString();
      }
    });
    const payload = {
      io_basic: {
        ...detailData.io_basic,
        ...this.bulkForm.value,
        ...{
          first_material_name: this.selectStyle[0]?.label || '',
          first_material_code: this.selectStyle[0]?.code || '',
          first_material_id: this.selectStyle[0]?.value || 0,
          second_material_name: this.selectStyle[1]?.label || '',
          second_material_code: this.selectStyle[1]?.code || '',
          second_material_id: this.selectStyle[1]?.value || 0,
          third_material_id: this.selectStyle[2]?.value || 0,
          third_material_name: this.selectStyle[2]?.label || '',
          third_material_code: this.selectStyle[2]?.code || '',
          order_date: this.bulkForm.value.order_date ? String(new Date(this.bulkForm.value.order_date).getTime()) : '',
          pre_material_completed_time: this.bulkForm.value.pre_material_completed_time
            ? new Date(this.bulkForm.value.pre_material_completed_time).getTime()
            : 0,
          extra_process_info,
        },
        sale_channel_id: this.bulkForm.value.sale_channel_id ?? null,
        sale_channel_name: this.sale_channel_options?.find((d: any) => d.value == this.bulkForm.value.sale_channel_id)?.label,
        biz_user_emp_id: this.bulkForm.value.biz_user_emp_id ? this.bulkForm.value.biz_user_emp_id.replace('employ-', '') : null,

        upfront_payment_rate: String(this.bulkForm.value.upfront_payment_rate),
        upfront_payment_amount: String(this.bulkForm.value.upfront_payment_amount),
      },
      io_lines: detailData.io_lines.filter((item: any) => {
        return totalLines.includes(`${item?.color_info?.color_id}${item?.color_info?.color_code}`);
      }),
      pos: detailData.pos,
    };
    return payload;
  }
  /**
   * 保存
   */
  save() {
    const isInvalid = this._validator.formIsInvalid(this.bulkForm);
    if (isInvalid) {
      this.notification.create('error', this._service.translateValue('flss.message.check-required'), '');
      return;
    }
    const invaild = this.bulkForm.get('io_code')?.invalid;
    this.bulkForm.get('io_code')?.markAsDirty();
    this.bulkForm.get('io_code')?.updateValueAndValidity();
    if (invaild) {
      this.notification.create('error', this._service.translateValue(this.translateName + '请检查订单需求号'), '');
      return;
    }
    if (this.fileGallery?.status === 'uploading') {
      this._msg.loading(this._service.translateValue(this.translateName + '附件正在上传，请稍后再试'));
      return;
    }
    if (this.imageGallery?.status === 'uploading') {
      this._msg.loading(this._service.translateValue(this.translateName + '图片正在上传，请稍后再试'));
      return;
    }
    let payload: any = this.handleValue();

    payload.io_basic['commit'] = false;
    const geturl = this.id === 'new' ? 'newBulk' : 'saveBulk';
    if (this.id !== 'new') {
      payload = {
        delete_info: {
          delete_pos: this.deletePos,
          delete_po_lines: this.getDeletePolines(),
        },
        io_info: payload,
      };
    }
    this._service[geturl](payload).subscribe((res) => {
      if (res.code === 200) {
        this._service.isChange = true;
        this._msg.create('success', this._service.translateValue('flss.success.save'));
        if (this.id === 'new') {
          this.id = res.data.id;
        }
        this._router.navigate(['/order/bulk/list', this.id]);
        this.copyId = 0;
        this.edit = false;
        this.getDetail(this.id);
        this._spUtil.removeObject('salesContractConvertOrderKey');
      }
    });
  }
  /**
   * 提交
   */
  async commit() {
    const invalid = await this._validator.formIsAsyncInvalid(this.bulkForm);
    if (invalid) {
      this.notification.create('error', this._service.translateValue('flss.message.check-required'), '');
      return;
    }
    if (this.detail.io_basic.order_pictures.length === 0) {
      this.notification.create('error', this._service.translateValue('common.message.至少上传一张款式图'), '');
      return;
    }
    if (this.detail.pos.length === 0) {
      this.notification.create('error', this._service.translateValue(this.translateName + '至少维护一个交付单数据'), '');
      return;
    }
    if (this.isDuPo()) {
      // 交付单号不可重复
      return;
    }
    let poCodeNull = false;
    let dueTimeNull = false;
    let sizeGroupNull = false;
    let po_code = '';
    this.detail.pos.find((item: any, i: number) => {
      if (!item.po_basic.po_code) {
        poCodeNull = true;
        this.notification.create(
          'error',
          this._service.translateValue(this.translateName + '颜色尺码下的第x个交付单的交付单号不可为空', { i: i + 1, po_code }),
          ''
        );
        this.tabIndex = i;
        return true;
      }
      if (!item.po_basic.spec_group_id) {
        po_code = item.po_basic.po_code;
        sizeGroupNull = true;
        this.notification.create(
          'error',
          this._service.translateValue(this.translateName + '颜色尺码下的第x个交付单的尺码组不可为空', { i: i + 1, po_code }),
          ''
        );
        this.tabIndex = i;
        return true;
      }
      if (!item.po_basic.due_time) {
        po_code = item.po_basic.po_code;
        dueTimeNull = true;
        this.notification.create(
          'error',
          this._service.translateValue(this.translateName + '颜色尺码下的第x个交付单的交付日期不可为空', { i: i + 1, po_code }),
          ''
        );
        this.tabIndex = i;
        return true;
      }
      return false;
    });
    if (poCodeNull || sizeGroupNull || dueTimeNull) {
      return;
    }
    // if ((this.colorSizeTableSum?.getTotalSum() ?? 0) < 1) {
    //   this.notification.create('error', this._service.translateValue(this.translateName + '交付单颜色尺码件数不能为空'), '');
    //   return;
    // }
    // if (this.colorSizeTableSum?.getTotalSum() > 100000000) {
    //   this.notification.create('error', this._service.translateValue(this.translateName + 'IO合计总数量不能超过100000000请修改'), '');
    //   return;
    // }
    if (this.fileGallery?.status === 'uploading') {
      this._msg.loading(this._service.translateValue(this.translateName + '附件正在上传，请稍后再试'));
      return;
    }
    if (this.imageGallery?.status === 'uploading') {
      this._msg.loading(this._service.translateValue(this.translateName + '图片正在上传，请稍后再试'));
      return;
    }
    let payload: any = this.handleValue();
    payload.io_basic.production_category_id = payload.io_basic.production_category_id
      ? Number(payload.io_basic.production_category_id)
      : null;
    payload.io_basic['commit'] = true;
    const notPass = [this.statusEnum.toSubmit, this.statusEnum.toModify].includes(this.detail.io_basic.order_status);
    const geturl = this.id === 'new' ? 'newBulk' : notPass ? 'saveBulk' : 'editBulk';
    if (this.id !== 'new') {
      payload = {
        delete_info: {
          delete_pos: this.deletePos,
          delete_po_lines: this.getDeletePolines(),
        },
        io_info: payload,
      };
    }
    const ref = this._flcModalService.confirmCancel({
      content: this._service.translateValue(this.translateName + '确定提交订单吗？提交后不可修改'),
      okText: this._service.translateValue('flss.btn.ok'),
      cancelText: this._service.translateValue('flss.btn.cancel'),
    });
    ref.afterClose.subscribe((res: any) => {
      if (!res) {
        return;
      } else {
        this._service[geturl](payload).subscribe((res) => {
          if (res.code === 200) {
            this._service.isChange = true;
            this._msg.create('success', this._service.translateValue('flss.success.submit'));
            if (this.id === 'new') {
              this.id = res.data.id;
            }

            this._router.navigate(['/order/bulk/list', this.id]);
            this.bulk_id = res.data.id;
            this.edit = false;
            this.copyId = 0;
            this.getDetail(this.id);
            this._spUtil.removeObject('salesContractConvertOrderKey');
          }
        });
      }
    });
  }
  /**
   * 获取删除的polines
   */
  getDeletePolines() {
    this.deletePoLines = [];
    this.detail.pos.forEach((item: any) => {
      item?.delete_po_lines?.forEach((con: any) => {
        if (con.id) {
          this.deletePoLines.push(con.id);
        }
      });
    });
    return this.deletePoLines;
  }
  /**
   * 款式图上传
   */
  uploadChange() {
    this._service.isChange = true;
  }
  /**
   * 展示全部
   */
  showAll() {
    this.toolTipVisible = false;
    this.showTop = !this.showTop;
    setTimeout(() => {
      this.toolTipVisible = true;
    });
  }
  /**
   * 退回修改
   */
  modify() {
    this.isConfirm = false;
    this.refuseReason = '';
    this.isVisible = true;
  }
  /**
   * 退回修改确定
   */
  handleOk() {
    this.isConfirm = true;
    if (!this.refuseReason) {
      return;
    }
    this.comLoading = true;
    this._service
      .auditReturn({ id: this.id, reason: this.refuseReason })
      .pipe(
        finalize(() => {
          this.comLoading = false;
        })
      )
      .subscribe((res: any) => {
        if (res.code === 200) {
          this.isVisible = false;
          this._service.isChange = true;
          this._msg.create('success', this._service.translateValue('flss.success.退回修改成功'));
          this.getDetail(this.id);
        }
      });
  }
  /**
   * 审核通过
   */
  pass() {
    const ref = this._flcModalService.confirmCancel({
      content: this._service.translateValue(this.translateName + '确定审核通过'),
      okText: this._service.translateValue('flss.btn.ok'),
      cancelText: this._service.translateValue('flss.btn.cancel'),
    });
    ref.afterClose.subscribe((res: any) => {
      if (!res) {
        return;
      } else {
        this._service.auditPass(Number(this.id)).subscribe((res: any) => {
          if (res.code === 200) {
            this._service.isChange = true;
            this._msg.create('success', this._service.translateValue('flss.success.pass'));
            this._service.batchCreateProductionTable({ bulk_order_ids: [this.id], operation_type: 1 }).subscribe((res) => {});
            this.getDetail(this.id);
          }
        });
      }
    });
  }
  /**
   * 取消订单
   */
  cancelOrder() {
    if (!this.detail.io_basic.deletable) {
      this.notification.create(
        'error',
        this._service.translateValue(this.translateName + '该大货单成衣加工外发或二次工艺外发订单已被工厂接单，无法取消'),
        ''
      );
      return;
    }
    const ref = this._flcModalService.confirmCancel({
      content: this._service.translateValue(this.translateName + '确定取消订单'),
      okText: this._service.translateValue('flss.btn.ok'),
      cancelText: this._service.translateValue('flss.btn.cancel'),
    });
    ref.afterClose.subscribe((res: any) => {
      if (!res) {
        return;
      } else {
        this._service.cancelOrder(Number(this.id)).subscribe((res: any) => {
          if (res.code === 200) {
            this._service.isChange = true;
            this._msg.create('success', this._service.translateValue('flss.success.cancel'));
            if (this.detail?.io_basic?.order_status !== this.statusEnum.toSubmit) {
              this._service.batchCreateProductionTable({ bulk_order_ids: [this.id], operation_type: 2 }).subscribe((res) => {});
            }
            this.getDetail(this.id);
          }
        });
      }
    });
  }
  /** 删除订单 */
  deleteOrder() {
    const ref = this._flcModalService.confirmCancel({
      content: this._service.translateValue(this.translateName + '确定删除订单'),
      okText: this._service.translateValue('flss.btn.ok'),
      cancelText: this._service.translateValue('flss.btn.cancel'),
    });
    ref.afterClose.subscribe((res: any) => {
      if (!res) {
        return;
      } else {
        this._service.deleteOrder(this.id).subscribe((res: any) => {
          if (res.code === 200) {
            this._service.isChange = true;
            this._msg.create('success', this._service.translateValue('flss.success.delete'));
            this.back();
          }
        });
      }
    });
  }
  /**
   * 返回
   */
  back() {
    if (this.edit && (this.bulkForm.dirty || JSON.stringify(this.originDetail) !== JSON.stringify(this.detail))) {
      const ref = this._flcModalService.confirmCancel({
        type: 'confirm-cancel',
        okText: this._service.translateValue('flss.btn.ok'),
        cancelText: this._service.translateValue('flss.btn.cancel'),
      });
      ref.afterClose.subscribe((res: any) => {
        if (res) {
          this._router.navigate(['/order/bulk/list']);
        }
      });
    } else {
      this._router.navigate(['/order/bulk/list']);
    }
  }

  handlePrePayment() {
    this._service.toReceipt({ order_uuid: [this.detail.io_basic.io_uuid] }).subscribe((res: any) => {
      if (res.code === 200) {
        this._router.navigate(['/settlement/receipt-management/list', 'new'], { queryParams: { id: res.data.id } });
      }
    });
  }
  /**
   * 编辑
   */
  editDetail() {
    this.edit = true;
    this.headerTopTitle = this.id === 'new' ? '新建订单需求' : '编辑订单需求';
  }
  /**
   * 删除交付单tab
   * @param  {any} echange_po_lines
   */
  closeTab(e: any) {
    const ref = this._flcModalService.confirmCancel({
      content: this._service.translateValue(this.translateName + '确定删除交付单X', { po_code: this.detail.pos[e.index].po_basic.po_code }),
      okText: this._service.translateValue('flss.btn.ok'),
      cancelText: this._service.translateValue('flss.btn.cancel'),
    });
    ref.afterClose.subscribe((res: any) => {
      if (!res) {
        return;
      } else {
        if (this.detail.pos[e.index].po_basic.id) {
          this.deletePos.push(this.detail.pos[e.index].po_basic.id);
        }
        this.detail.pos.splice(e.index, 1);
        this.detail.pos.forEach((item: any) => {
          item.po_lines = [...item.change_po_lines];
        });
        this.processedData.splice(e.index, 1);
        this.sumTotal();
        this.processMergedTableData();
        this.tableTotals.splice(e.index, 1);
        this.calculateUpfrontPayment();
      }
    });
  }
  // 自动生成po
  generateCode() {
    let code = '';
    // 过滤掉手动更改过code的问题点
    const list = this.detail.pos.filter((item: any) => {
      return !this._flcUtil.isNilOrEmptyStr(item.po_basic.po_code);
    });
    // 如果是新建问题点，并且添加的第一行，那么给code赋值从后端获取的code
    if (list.length === 0 && this.id === 'new') {
      code = (this.poNumber + 1).toString().padStart(3, '0');
    }

    // 如果不是新建问题点，那么code赋值从最后一个自动生成的code加1，如果code被修改过，那么不使用（onChangeInput事件有处理code_number的值）
    if (this.id !== 'new' || list.length !== 0) {
      const code_number_list: number[] = []; // 存自动生成的code_number
      list.forEach((item: any) => {
        if (item.po_basic.code_number) {
          code_number_list.push(item.po_basic.code_number);
        }
      });

      // 如果code_number_list为空，说明没有自动生成的code，那么使用绑定的code，如果绑定的code为空，那么使用001
      if (code_number_list.length === 0) {
        code = (this.poNumber + 1).toString().padStart(3, '0') || '001';
      } else {
        const max_number = Math.max(...code_number_list); // 获取自动生成的最大的code_number
        code = (max_number + 1).toString().padStart(3, '0'); // 最小三位数，不足补0
      }
    }

    return {
      code: code,
      code_number: isNaN(Number(code)) ? null : Number(code),
    };
  }
  /**
   * 自动生成交付单
   */
  autoPo() {
    const canNot: boolean = this.isNullPo() || (this.detail.pos.length === 1 && (this.detail.pos[0]?.po_lines ?? []).length === 0);

    if (!canNot) {
      return;
    }
    const code = this.bulkForm.get('style_code')?.value;
    this._service.getStyleCodeColorSize(code).subscribe((res) => {
      if (res.code === 200) {
        if (res.data) {
          const obj = this.detail.pos[0];
          obj.editable = false;
          obj.po_basic.deletable = true;
          obj.po_basic.spec_group_id = res.data?.spec_group_id ?? null;
          obj.po_basic.spec_group_name = res.data?.spec_group_name ?? null;
          obj.po_basic.spec_group_code = res.data?.spec_group_code ?? null;

          const commonLine: any[] = [];
          res.data.color_list?.forEach((colorItem: any) => {
            res.data.size_list?.forEach((sizeItem: any) => {
              commonLine.push({
                id: 0,
                line_uuid: '',
                indexing: res.data.size_list!.indexOf(sizeItem) + 1,
                color_info: { color_code: colorItem.color_code, color_id: colorItem.color_id, color_name: colorItem.color_name },
                size_info: { spec_code: sizeItem.spec_code, spec_id: sizeItem.spec_id, spec_size: sizeItem.spec_size },
                deletable: true,
              });
            });
          });
          obj['po_lines'] = commonLine;
          obj['change_po_lines'] = commonLine;

          this.detail.pos[0] = obj;
          this.tabIndex = this.detail.pos.length - 1;

          // 更新nz-table的processedData
          this.updateProcessedData(0);
          this.sumTotal();
        }
      }
    });
  }
  /**
   * 新增交付单
   */
  addPo() {
    if (this.isNullPo()) {
      return;
    }
    if (this.isDuPo()) {
      return;
    }
    this.detail.pos.forEach((item: any) => {
      item.editable = false;
    });
    const { code, code_number } = this.generateCode();
    this.detail.pos.push({
      editable: true,
      po_basic: { po_code: `${code}`, code_number: `${code_number}`, due_time: '', deletable: true, customer_due_time: '' },
    });
    this.tabIndex = this.detail.pos.length - 1;

    // 为新增的交付单创建一个空行，确保用户可以添加数据
    const newTabIndex = this.detail.pos.length - 1;
    this.ensureEmptyRowExists(newTabIndex);

    // 更新 processedData
    this.updateProcessedData(newTabIndex);

    if (this.factoryCodes.includes(this.userInfo?.factory_code)) {
      this.setSizeTable(this.detail.pos?.length - 1);
    }
    // 新增po input聚焦
    setTimeout(() => {
      (document.querySelector('.po-input') as HTMLInputElement).focus();
    }, 350);
  }
  /**
   * 复制交付单
   */
  copyPo(i: number) {
    if (this.isNullPo()) {
      return;
    }
    this.detail.pos.forEach((item: any) => {
      item.editable = false;
    });
    const { code, code_number } = this.generateCode();
    const obj = JSON.parse(JSON.stringify(this.detail.pos[i]));
    obj.editable = true;
    obj.po_basic.po_code = `${code}`;
    obj.po_basic.code_number = `${code_number}`;
    obj.po_basic.id = 0;
    obj.po_basic.deletable = true;
    obj.po_basic.po_unique_code = '';
    (obj.change_po_lines ?? []).forEach((item: any) => {
      item.id = 0;
      item.line_uuid = '';
      item.deletable = true;
    });
    obj['po_lines'] = obj.change_po_lines || [];
    this.detail.pos.push(obj);

    // 复制后重新处理processedData，确保数据正确对应
    const newTabIndex = this.detail.pos.length - 1;
    this.updateProcessedData(newTabIndex);

    this.tabIndex = this.detail.pos.length - 1;
    setTimeout(() => {
      (document.querySelector('.po-input') as HTMLInputElement).focus();
    }, 350);
    this.sumTotal();

    // 重新计算合计表格数据
    this.processMergedTableData();
    this.calculateTabTotals(this.tabIndex);
    this.calculateUpfrontPayment();
  }
  /**
   * 编辑交付单
   */
  editPo(i: number) {
    this.detail.pos[i].editable = !this.detail.pos[i].editable;
    setTimeout(() => {
      (document.querySelector('.po-input') as HTMLInputElement).focus();
    }, 350);
  }
  /**
   * 是否为空交付单
   */
  isNullPo() {
    try {
      const tabName = this.detail.pos.find((item: any) => !item.po_basic.po_code.trim());
      if (tabName) {
        this._notice.error(this._service.translateValue(this.translateName + '你还有未填写交付单号的交付单'), '');
      }
      return tabName;
    } catch (_) {
      return null;
    }
  }
  /**
   * 交付单是否重复
   */
  isDuPo() {
    const res = this.detail.pos.filter((item: any, index: number, self: any) => {
      return (
        self.findIndex((con: any) => {
          return con.po_basic.po_code === item.po_basic.po_code;
        }) === index
      );
    });
    if (res.length !== this.detail.pos.length) {
      this._notice.error(this._service.translateValue(this.translateName + '交付单号不能重复'), '');
    }
    return res.length !== this.detail.pos.length;
  }
  /**
   * 交付单输入框失焦
   */
  tabInputBlur(e: string) {
    if (!e) {
      return;
    }
    const dName = this.detail.pos.filter((ele: any) => {
      return ele.po_basic.po_code === e;
    });
    if (dName.length > 1) {
      this._notice.error(this._service.translateValue(this.translateName + '交付单号不能重复'), '');
      return;
    }
    this.detail.pos[this.tabIndex].editable = false;
  }
  /**
   * 二次工艺改变值
   * @param  {any} e
   */
  changeExtra(e: any) {
    // if (e.length > 0) {
    //   if (e[0] === 0) {
    //     this.extraList.forEach((item: any, i: number) => {
    //       if (i > 0) {
    //         item.disabled = true;
    //       }
    //     });
    //   } else {
    //     this.extraList.forEach((item: any, i: number) => {
    //       if (i === 0) {
    //         item.disabled = true;
    //       }
    //     });
    //   }
    // } else {
    //   this.extraList.forEach((item: any) => {
    //     item.disabled = false;
    //   });
    // }
  }
  /**
   * 输入地址
   * @param  {any} e 值
   * @param  {number} i 第几个po
   */
  addressChange(e: any, i: number) {
    this.detail.pos[i].po_basic['country_name'] = e[0]?.label || '';
    this.detail.pos[i].po_basic['country_id'] = e[0]?.value || 0;
    this.detail.pos[i].po_basic['province_name'] = e[1]?.label || '';
    this.detail.pos[i].po_basic['province_id'] = e[1]?.value || 0;
    this.detail.pos[i].po_basic['city_name'] = e[2]?.label || '';
    this.detail.pos[i].po_basic['city_id'] = e[2]?.value || 0;
    this.detail.pos[i].po_basic['district_name'] = e[3]?.label || '';
    this.detail.pos[i].po_basic['district_id'] = e[3]?.value || 0;
  }
  // 获取省市区
  getRegions() {
    return new Promise((resolve) => {
      this._service.getAddress().subscribe((res: any) => {
        if (res.code == 200) {
          this.regionList = this._service.onTransOption(res?.data?.children ?? []);
          resolve(true);
        }
      });
    });
  }
  /**
   * 打开二次工艺下拉框
   */
  extraOpen() {
    this.detail.io_basic.extra_process_info.forEach((item: any) => {
      this.extraList.forEach((con: any) => {
        if (con.value === item.extra_process_id && !item.deletable) {
          con.disabled = true;
        }
      });
    });
  }
  /**
   * 成衣加工数据
   */
  getProdProgress(io_uuid: string) {
    this._service.getProdProgress(io_uuid).subscribe((res: any) => {
      if (res.code === 200) {
        this.prodData = res.data;
      }
    });
  }
  /**
   * 二次工艺数据
   */
  getOutData(io_uuid: string) {
    this._service.getOutData(io_uuid).subscribe((res: any) => {
      this.outData = { list: res.data.list };
      this.outData.list.forEach((item: any) => {
        if (item.cut_count > 0) {
          this.outData['cutCount'] = true;
        }
        if (item.outsource_qty > 0) {
          this.outData['outsourceQty'] = true;
        }
        if (item.received_qty > 0) {
          this.outData['receivedQty'] = true;
        }
        if (item.finished_qty > 0) {
          this.outData['finishedQty'] = true;
        }
        if (item.qualified_qty > 0) {
          this.outData['qualifiedQty'] = true;
        }
        if (item.sent_qty > 0) {
          this.outData['sendQty'] = true;
        }
      });
    });
  }
  /**
   * 获取详情
   */
  getDetail(id: number | string, fromInit = false) {
    this._service
      .getDetail(Number(id), { id: Number(id), cache: true, production_type: 3, from: 'bulk_order' })
      .subscribe(async (res: any) => {
        if (res.code === 200) {
          res?.data?.pos?.forEach((po: any) => {
            const fob_price = po?.po_basic?.fob_price;
            const total_fob = po?.po_basic?.total_fob;
            const total_sales_cad = po?.po_basic?.total_sales_cad;
            if (fob_price && Number(fob_price) === 0) {
              po.po_basic.fob_price = null;
            }
            if (total_fob && Number(total_fob) === 0) {
              po.po_basic.total_fob = null;
            }
            if (total_sales_cad && Number(total_sales_cad) === 0) {
              po.po_basic.total_sales_cad = null;
            }
          });
          if (res.data?.io_basic?.order_status === OrderStatus.auditPass) {
            // 审核通过
            this.getProdProgress(res.data?.io_basic?.io_uuid);
            this.getOutData(res.data?.io_basic?.io_uuid);
          }
          this.loading = false;
          this.headerTopTitle = this.id === 'new' ? '新建订单需求' : '订单需求详情';

          if (res.data?.io_basic?.process_factory_code) {
            this.merchandiser_options =
              this.process_factory_options.find((d: any) => d.process_factory_code === res.data?.io_basic?.process_factory_code)
                ?.merchandiser_list || [];
          }

          let style = '';
          const extra_process_list: any[] = [];
          let styleOption: any[] = [];
          if (res.data.io_basic.first_material_name) {
            res.data.io_basic.first_material_id = Number(res.data.io_basic.first_material_id);
            res.data.io_basic.second_material_id = Number(res.data.io_basic.second_material_id);
            res.data.io_basic.third_material_id = Number(res.data.io_basic.third_material_id);
            this.selectStyle = [
              {
                label: res.data.io_basic.first_material_name,
                value: res.data.io_basic.first_material_id,
                code: res.data.io_basic.first_material_code,
              },
              {
                label: res.data.io_basic.second_material_name,
                value: res.data.io_basic.second_material_id,
                code: res.data.io_basic.second_material_code,
              },
              {
                label: res.data.io_basic.third_material_name,
                value: res.data.io_basic.third_material_id,
                code: res.data.io_basic.third_material_code,
              },
            ];
            styleOption = [
              res.data.io_basic.first_material_name,
              res.data.io_basic.second_material_name,
              res.data.io_basic.third_material_name,
            ];
            style =
              res.data.io_basic.first_material_name +
              '-' +
              res.data.io_basic.second_material_name +
              '-' +
              res.data.io_basic.third_material_name;
          }
          res.data.io_basic.extra_process_info.forEach((item: any) => {
            extra_process_list.push(item.extra_process_id);
          });
          if (!this.bulkOrderCode && res.data.io_basic.brand_id) {
            const bulkCodeRes = await firstValueFrom(this._service.getBulkOrderCode(res.data.io_basic.brand_id));
            this.bulkOrderCode = bulkCodeRes.code === 200 ? bulkCodeRes.data.code : null;
          }
          if (this.copyId) {
            // 复制大货单号为空
            res.data.io_basic.io_code = this.bulkOrderCode;
            res.data.io_basic.id = 0;
            res.data.io_basic.deletable = true;
            res.data.io_basic.order_status = 0;
            res.data.io_basic.order_status_value = '';
            res.data.io_basic.io_uuid = '';
            res.data.io_basic.order_category = 2;

            // 付款方式
            // res.data.upfront_payment_rate = '';

            // 复制，跟单员、外发加工厂为空
            res.data.io_basic.merchandiser_name = null;
            res.data.io_basic.merchandiser_user_id = null;
            res.data.io_basic.process_factory_code = null;
            res.data.io_basic.process_factory_name = null;

            res.data.io_basic.extra_process_info.forEach((con: any) => {
              con.deletable = true;
            });
            res.data.pos.forEach((item: any) => {
              item.po_basic.id = 0;
              item.po_basic.deletable = true;
              item.po_basic.po_unique_code = '';
              item.po_lines.forEach((con: any) => {
                con.id = 0;
                con.line_uuid = '';
                con.deletable = true;
                con.changeable = true;
              });
            });
          }
          res.data.pos.forEach((item: any) => {
            const index: number = Number(item.po_basic.po_code) ?? 0;
            this.poNumber = max([this.poNumber, index]) ?? this.poNumber;
            item.po_basic.spec_group_id = Number(item.po_basic.spec_group_id);
            // 重新计算indexing以确保符合新的逻辑
            this.recalculateIndexingForLoadedData(item);
            const maxIndexing =
              item?.po_lines.length > 0
                ? item.po_lines.reduce((prev: any, current: any) => (prev.indexing > current.indexing ? prev : current)).indexing
                : 0;
            item['maxIndexing'] = maxIndexing;
            item['change_po_lines'] = item.po_lines;
            if (item.po_basic?.country_name != '中国') {
              if (item.po_basic?.country_id) {
                item['addDefault'] = [item.po_basic?.country_id];
              }
            } else {
              if (item.po_basic?.country_id) {
                item['addDefault'] = [
                  item.po_basic?.country_id,
                  item.po_basic?.province_id,
                  item.po_basic?.city_id,
                  item.po_basic?.district_id,
                ];
              }
            }
            if (item.po_basic.due_time) {
              item.po_basic.due_time = item.po_basic.due_time ? format(Number(item.po_basic.due_time), 'yyyy/MM/dd') : '';
            }
            if (item.po_basic.customer_due_time) {
              item.po_basic.customer_due_time = item.po_basic.customer_due_time
                ? format(Number(item.po_basic.customer_due_time), 'yyyy/MM/dd')
                : '';
            }
          });
          const order_date = res.data.io_basic.order_date ? format(Number(res.data.io_basic.order_date), 'yyyy/MM/dd') : '';
          const pre_material_completed_time =
            res.data.io_basic.pre_material_completed_time > 0
              ? format(Number(res.data.io_basic.pre_material_completed_time), 'yyyy/MM/dd')
              : '';
          this.detail = res.data;
          Object.assign(this.detail.io_basic, {
            style,
            order_date,
            styleOption: styleOption,
            extra_process_list: extra_process_list,
            pre_material_completed_time,
            biz_user_emp_id: res.data.io_basic.biz_user_emp_id ? 'employ-' + res.data.io_basic.biz_user_emp_id : null,
          });

          if (this.copyId) {
            this.edit = true;
          }
          this.extraOpen();
          this.changeExtra(extra_process_list);
          this.handleForm(this.detail);

          this.processDataForTable();
          if (this.copyId) {
            this.check_is_merchandiser();
          }
          this.styleCodeDetail.uuid = this.detail.io_basic.style_code_uuid;
          if (this._route.snapshot.queryParams?.mode === 'edit' && fromInit) {
            this.edit = true;
          }
          if (!this.factoryCodes.includes(this.userInfo?.factory_code)) {
            this.orderProductionTypeChange(res.data.io_basic.order_production_type);
          }
        }
        this.originDetail = JSON.parse(JSON.stringify(this.detail));
        // 审核通过且没加工厂时，需看生成的大货订单有没有指派加工厂， 有，则订单需求不可再填加工厂 bug 69038
        if ([11].includes(this.detail?.io_basic?.order_status) && !this.detail?.io_basic?.process_factory_code) {
          this.checkHasFactory();
        }
      });
  }

  process_factory_disabled = false;
  checkHasFactory() {
    this._service.garmentsList(this.detail?.io_basic?.io_code).subscribe((res: any) => {
      if (res?.code === 200) {
        const op = res?.data?.data?.find((item: any) => item?.io_code === this.detail?.io_basic?.io_code);
        this.process_factory_disabled = op?.distribution_factory_name?.filter((s: string) => !!s)?.length > 0;
      }
    });
  }

  get processFactoryDisabled() {
    return (
      this.process_factory_disabled ||
      (!!this.detail?.io_basic?.first_pass_at &&
        !!this.detail?.io_basic?.process_factory_code &&
        [4, 11].includes(this.detail?.io_basic?.order_status))
    );
  }

  handleForm(data?: any) {
    const ioBasic = data?.io_basic;
    this.bulkForm = this._fb.group({});
    this.bulkForm = this._fb.group({
      io_code: [ioBasic?.io_code || '', [Validators.required], [this._service.uniqueValidator(ioBasic?.io_code)]], // 大货单号
      order_category: [ioBasic?.order_category || 1], // 订单类型
      order_production_type: [ioBasic?.order_production_type || 1], // 生产类型
      style_code: [ioBasic?.style_code || '', [Validators.required]], // 款式编码
      customer: [ioBasic?.customer || '', [Validators.required]], // 客户名称
      customer_id: [ioBasic?.customer_id || null], // 客户ID
      category: [ioBasic?.category || ''], // 品名
      brand_id: [ioBasic?.brand_id], // 品牌
      style: [ioBasic?.styleOption || [], [Validators.required]], // 款式分类
      order_date: [ioBasic?.order_date || format(startOfDay(new Date()), 'yyyy/MM/dd'), [Validators.required]], // 下单日期
      contract_number: [ioBasic?.contract_number || ''], // 销售单号
      extra_process_info: [ioBasic?.extra_process_list || []], // 二次工艺
      remark: [ioBasic?.remark || ''], // 备注
      sale_channel_id: [ioBasic?.sale_channel_id || null], // 销售渠道
      quality_level: [ioBasic?.quality_level || null], // 品质
      production_category_id: [ioBasic?.production_category_id ? String(ioBasic.production_category_id) : null], // 产品类型id
      production_category_name: [ioBasic?.production_category_name || null], // 产品类型name
      payment_condition: [ioBasic?.payment_condition || null], //付款条件
      fast_reply: [ioBasic?.fast_reply || null], // 是否快反
      third_quality_check: [ioBasic?.third_quality_check || null], // 是否需要第三方质检
      customs_clearance: [ioBasic?.customs_clearance || null], // 是否需要报关
      order_classification: [ioBasic?.order_classification || null], // 订单类型
      store: [ioBasic?.store || ''], // 商店
      company_code: [ioBasic?.company_code || ''], // 公司代码
      composition: [ioBasic?.composition || ''], // 成分
      department: [ioBasic?.department || ''], // 部门
      gen_dept_id: [ioBasic?.gen_dept_id || null], // 部门id
      contract_price: [ioBasic?.contract_price || ''], // 合同价格
      fabric: [ioBasic?.fabric || ''], // 面料
      color: [ioBasic?.color || ''], // 配色
      sample_requirement: [ioBasic?.sample_requirement || ''], // 样衣要求
      printing_embroidery: [ioBasic?.printing_embroidery || ''], // 印花绣花
      sew_accessory: [ioBasic?.sew_accessory || ''], // 车缝辅料
      consolidation_accessory: [ioBasic?.consolidation_accessory || ''], // 后整辅料
      accessory_remark: [ioBasic?.accessory_remark || ''], // 辅料备注
      special_requirement: [ioBasic?.special_requirement || ''], // 特殊要求
      process_factory_code: [ioBasic?.process_factory_code || null], // 外发加工厂
      merchandiser_user_id: [ioBasic?.merchandiser_user_id || null], // 跟单员
      merchandiser_name: [ioBasic?.merchandiser_name || null], // 跟单员
      pre_material_completed_time: [ioBasic?.pre_material_completed_time || null], // 预计物料齐套日期
      biz_user_emp_id: [ioBasic?.biz_user_emp_id || null], // 业务员
      is_pre_order: [ioBasic?.is_pre_order || 2, [Validators.required]], // 是否预排单
      is_use_plan: [ioBasic?.is_use_plan || false, [Validators.required]], // 是否使用生产计划
      urgent_status: [ioBasic?.urgent_status], //是否急单
      pro_office: [ioBasic?.pro_office || null], // 办事处
      pro_office_name: [ioBasic?.pro_office_name || null], // 办事处
      cat_name: [ioBasic?.cat_name || null], // 品类名称
      cat_no: [ioBasic?.cat_no || null], // 品类编码
      lead_time: [ioBasic?.lead_time || null], // 生产周期
      lead_time_name: [ioBasic?.lead_time_name || null], // 生产周期
      season: [ioBasic?.season || null], // 季节
      season_name: [ioBasic?.season_name || null], // 季节
      origin_country: [ioBasic?.origin_country || null], // 生产国
      origin_country_name: [ioBasic?.origin_country_name || null], // 生产国,
      category_type: [ioBasic?.category_type || null], // 品类,
      category_type_name: [ioBasic?.category_type_name || null], // 品类,
      order_labels: [ioBasic?.order_labels || []], // 订单标签,

      upfront_payment_amount: [ioBasic?.upfront_payment_amount || null],
      upfront_payment_rate: [ioBasic?.upfront_payment_rate || null],
      payment_id: [!ioBasic ? null : ioBasic.payment_id ? String(ioBasic.payment_id) : null],
      payment_name: [ioBasic?.payment_name || null],
      currency_id: [ioBasic?.currency_id || null, [Validators.required]],
      currency_name: [ioBasic?.currency_name || null],
    });
    // 大货订单已提交，不可以修改是否使用生产计划
    if (ioBasic?.is_outsourcing_submit && !this.copyId) {
      this.bulkForm.get('is_use_plan')?.disable();
    }
  }

  getOrderOutgoingTypeDisplayType(type: any) {
    if (type == null || type == undefined) return '-';
    const data = this.order_outgoing_options.find((e: any) => e.value == type);
    if (data) {
      return data.label;
    }
    return '-';
  }

  getOrderProdutionTypeDisplayType(type: any) {
    if (type == null || type == undefined) return '-';
    const data = this.order_production_options.find((e: any) => e.value == type);
    if (data) {
      return data.label;
    }
    return '-';
  }

  getBeandDisplayType(type: any) {
    if (type == null || type == undefined) return '-';
    const data = this.brandList.find((e: any) => e.value == type);
    if (data) {
      return data.label;
    }
    return '-';
  }

  onSelectProductionCategory(e: any) {
    this.bulkForm.get('production_category_name')?.setValue(e?.selectLine?.label);
  }

  greaterThanValidator = (min: number): ValidatorFn => {
    return (control: AbstractControl): ValidationErrors | null => {
      if (control.value == null || control.value === '') {
        return null;
      } else {
        return control.value <= min ? { required: { required: true } } : null;
      }
    };
  };

  printData() {
    this.bulkPrint.onPrint();
  }

  fontLengthMax(value: string, key: string, max: number) {
    if (value.length > max) {
      this.bulkForm.get(key)?.setValue(value.substring(0, max));
    }
  }

  onOpenDrawerCustom() {
    const formConfig = [
      {
        title: this._service.translateValue('common.基本信息'),
        toggle: true,
        config: this.customBaseInfoConfig,
        datalist: this.basicList,
      },
    ];
    this._drawer.openDrawer({
      title: this._service.translateValue('flss.common.自定义/业务配置'),
      content: BaseInfoCustomContainerComponent,
      placement: 'right',
      width: '480px',
      contentParams: {
        configList: formConfig,
        saveConfig: (datalist: any[]) => {
          this.basicList = datalist.find((item: any) => item.title === this._service.translateValue('common.基本信息'))?.datalist;
          this._drawer.closeDrawer();
        },
        cancelConfig: () => {
          this._drawer.closeDrawer();
        },
      },
    });
  }

  get order_picture_show() {
    return (this.basicList as any[] | null)?.find((item: any) => item.code === 'order_pictures')?.visible;
  }

  // 获取BOM信息-面料信息
  onGetBomMaterialDescription(value: string) {
    this.bulkForm.get('fabric')?.setValue(value);
  }

  // 交付单字段配置
  customDFWOConfig = {
    localKey: 'bulkCustomDFWO',
    version: '1.2.1',
    translateName: this.translateName,
  };
  onOpenDrawerCustomByDFWO() {
    const formConfig = [
      {
        title: this._service.translateValue(this.translateName + '交付单'),
        toggle: true,
        config: this.customDFWOConfig,
        datalist: this.DFWOConfigList,
      },
    ];
    this._drawer.openDrawer({
      title: this._service.translateValue('flss.common.自定义/业务配置'),
      content: BaseInfoCustomContainerComponent,
      placement: 'right',
      width: '480px',
      contentParams: {
        configList: formConfig,
        saveConfig: (datalist: any[]) => {
          this.DFWOConfigList = datalist.find(
            (item: any) => item.title === this._service.translateValue(this.translateName + '交付单')
          )?.datalist;
          this._drawer.closeDrawer();
        },
        cancelConfig: () => {
          this._drawer.closeDrawer();
        },
      },
    });
  }

  formatterText(array = []) {
    if (array?.length) {
      return array
        ?.map((item: string) => {
          return this._service.translateValue(this.translateName + item);
        })
        ?.join(',');
    }
    return null;
  }
  private getUserBaseInfo() {
    this._service.getUserBaseInfo().subscribe((res) => {
      if (res.code === 200) {
        this.bulkForm.get('gen_dept_id')?.setValue(res.data.dept_id);
        this.bulkForm.get('department')?.setValue(res.data.dept_name);
      }
    });
  }

  private getDepartment() {
    this._service.getDepartment().subscribe((res) => {
      if (res.code === 200) {
        this.departmentList = this.handleDepartmentOption(res.data.children, []);
      }
    });
  }

  private handleDepartmentOption(data: any[], option: any[]) {
    data.forEach((item) => {
      if (!item.is_relation && item.key !== -1) {
        option.push({
          label: item.title,
          value: item.key,
        });
      }
      item.children?.length && this.handleDepartmentOption(item.children, option);
    });
    return option;
  }

  customerChange(value: any, item: any) {
    if (item.code === 'customer') {
      const selectedCustomer = this.selectOptions?.customer.find((e: any) => e.label === value);
      if (selectedCustomer) {
        // 设置customer_id
        this.bulkForm.get('customer_id')?.setValue(selectedCustomer.value);

        // 设置货币单位
        let unit_name = selectedCustomer?.ext_info?.unit_name;
        if (unit_name) {
          this.bulkForm.get('currency_id')?.setValue(this.currency_id_options.find((e: any) => e.label === unit_name)?.value);
        }
      } else {
        // 如果没有找到对应的客户，清空customer_id
        this.bulkForm.get('customer_id')?.setValue(null);
      }
    }
  }

  numberInputChange(value: any, item: any) {
    if (this.isCalculating) {
      this.isCalculating = false;
      return;
    }
    this.isCalculating = true;
    if (item.code === 'upfront_payment_rate') {
      this.onUpfrontRateChange(value);
    } else if (item.code === 'upfront_payment_amount') {
      this.onUpfrontAmountChange(value);
    }
  }

  processDataForTable() {
    // 确保detail和pos存在
    if (!this.detail?.pos) {
      return;
    }

    // 遍历每个tab的数据
    this.detail.pos.forEach((tab: any, tabIndex: any) => {
      // 获取当前tab的po_lines数据
      const poLines = tab.po_lines || [];

      // 🔥 修复：为初始化数据建立颜色首次出现顺序映射
      const colorFirstAppearanceMap = new Map<string, number>();
      poLines.forEach((line: any, index: number) => {
        const colorKey = `${line.color_info.color_id || 'null'}-${line.color_info.color_name || ''}`;
        if (!colorFirstAppearanceMap.has(colorKey)) {
          colorFirstAppearanceMap.set(colorKey, index);
        }
      });

      // 按颜色分组
      const colorGroups = new Map<string, LinesType[]>();
      poLines.forEach((line: any) => {
        const colorKey = `${line.color_info.color_id}-${line.color_info.color_name}`;
        if (!colorGroups.has(colorKey)) {
          colorGroups.set(colorKey, []);
        }
        colorGroups.get(colorKey)?.push(line);
      });

      // 转换数据格式
      const processedData: processItem[] = [];

      // 遍历每个颜色组
      colorGroups.forEach((lines, colorKey) => {
        // 获取该颜色组的行数
        const colorRowCount = lines.length;

        // 按尺码排序
        lines.sort((a, b) => {
          return a.size_info.spec_id - b.size_info.spec_id;
        });

        // 处理每一行数据
        lines.forEach((line, index) => {
          // 获取颜色的首次出现顺序
          const colorKeyForOrder = `${line.color_info.color_id || 'null'}-${line.color_info.color_name || ''}`;
          const colorFirstAppearanceIndex = colorFirstAppearanceMap.get(colorKeyForOrder) || 0;

          const item: processItem = {
            color: line.color_info.color_name,
            size: line.size_info.spec_size,
            qty: line.qty || 0,
            unit_price: Number(line.unit_price || 0),
            sku_amount: Number(line.sku_amount || 0),
            excess_rate: Number(line.excess_rate || 0),
            excess_quantity: line.excess_quantity || 0,
            deficiency_rate: Number(line.deficiency_rate || 0),
            deficiency_quantity: line.deficiency_quantity || 0,
            colorRowspan: index === 0 ? colorRowCount : 0, // 只在颜色组的第一行设置rowspan
            unitPriceRowspan: index === 0 ? colorRowCount : 0, // 单价跟随颜色合并
            originalLine: line,
            originalIndex: poLines.indexOf(line), // 在原始数组中的索引
            colorFirstAppearanceIndex: colorFirstAppearanceIndex, // 🔥 设置颜色首次出现顺序
          };

          // 计算金额和数量
          this.calculateItemTotals(item);
          this.calculateOverflowAndShortage(item);

          // 同步计算后的金额到原始数据
          item.originalLine.sku_amount = String(item.sku_amount || 0);

          processedData.push(item);
        });
      });

      // 保存处理后的数据到对应的tab索引
      this.processedData[tabIndex] = processedData;

      // 计算当前tab的合计数据
      this.calculateTabTotals(tabIndex);
    });
  }

  // 处理数量变化
  onQuantityChange(item: processItem, tabIndex: number) {
    this.calculateItemTotals(item);
    this.calculateOverflowAndShortage(item);
    // 同步计算后的金额到原始数据
    item.originalLine.sku_amount = String(item.sku_amount || 0);
    this.calculateTabTotals(tabIndex);
    this.calculateUpfrontPayment();
  }

  // 清除溢装比例
  clearOverflowRatio(item: processItem, tabIndex: number) {
    item.excess_rate = 0;
    this.calculateOverflowAndShortage(item);
    this.calculateTabTotals(tabIndex);
  }

  // 清除短装比例
  clearShortageRatio(item: processItem, tabIndex: number) {
    item.deficiency_rate = 0;
    this.calculateOverflowAndShortage(item);
    this.calculateTabTotals(tabIndex);
  }

  // 将溢装比例应用到所有行
  applyOverflowRatioToAll(item: processItem, tabIndex: number) {
    const ratio = item.excess_rate;
    if (ratio !== null && ratio !== undefined) {
      this.modal.confirm({
        nzTitle: '确认',
        nzContent: '是否更新当前订单需求所有溢装比例？',
        nzOnOk: () => {
          const ratio = item.excess_rate;
          this.processedData[tabIndex].forEach((row: any) => {
            row.excess_rate = ratio;
            this.calculateOverflowAndShortage(row);

            // 同步更新原始数据 - 确保转换为字符串
            // if (row.originalLine) {
            //   row.originalLine.excess_rate = String(ratio || '');
            //   row.originalLine.excess_quantity = row.excess_quantity;
            // }
          });
          this.calculateTabTotals(tabIndex);
        },
      });
    }
  }

  // 将短装比例应用到所有行
  applyShortageRatioToAll(item: processItem, tabIndex: number) {
    const ratio = item.deficiency_rate;
    if (ratio !== null && ratio !== undefined) {
      this.modal.confirm({
        nzTitle: '确认',
        nzContent: '是否将当前短装比例应用到所有行？',
        nzOnOk: () => {
          const ratio = item.deficiency_rate;
          this.processedData[tabIndex].forEach((row: any) => {
            row.deficiency_rate = ratio;
            this.calculateOverflowAndShortage(row);

            // 同步更新原始数据
            // if (row.originalLine) {
            //   row.originalLine.deficiency_rate = ratio;
            //   row.originalLine.deficiency_quantity = row.deficiency_quantity;
            // }
          });
          this.calculateTabTotals(tabIndex);
        },
      });
    }
  }

  // 计算单个项目的总金额
  private calculateItemTotals(item: processItem) {
    if (typeof item.qty === 'number' && typeof item.unit_price === 'number') {
      item.sku_amount = Number(this._flcUtil.accMul(item.qty, item.unit_price).toFixed(2));
    } else {
      item.sku_amount = 0;
    }
  }

  // 计算溢装数和短装数
  private calculateOverflowAndShortage(item: processItem) {
    // 计算溢装数 - 直接取整，不四舍五入
    if (typeof item.excess_rate === 'number' && typeof item.qty === 'number') {
      item.excess_quantity = Math.floor(this._flcUtil.accMul(item.qty, this._flcUtil.accDiv(item.excess_rate, 100)));
    } else {
      item.excess_quantity = 0;
    }

    // 计算短装数 - 直接取整，不四舍五入
    if (typeof item.deficiency_rate === 'number' && typeof item.qty === 'number') {
      item.deficiency_quantity = Math.floor(this._flcUtil.accMul(item.qty, this._flcUtil.accDiv(item.deficiency_rate, 100)));
    } else {
      item.deficiency_quantity = 0;
    }
  }

  // 计算订单总金额
  private calculateTotalAmount(): number {
    let totalAmount = 0;
    this.processedData.forEach((tabData: any[]) => {
      if (tabData) {
        tabData.forEach((item) => {
          totalAmount += item.sku_amount || 0;
        });
      }
    });
    return totalAmount;
  }

  // 计算预收款和预收比例
  private calculateUpfrontPayment() {
    const totalAmount = this.calculateTotalAmount();
    const upfrontRate = this.bulkForm.get('upfront_payment_rate')?.value;
    const upfrontAmount = this.bulkForm.get('upfront_payment_amount')?.value;

    if (totalAmount > 0) {
      if (upfrontRate !== null && upfrontRate !== undefined && upfrontRate !== '') {
        // 如果预收比例有值，计算预收款
        const calculatedAmount = Number(this._flcUtil.accMul(totalAmount, this._flcUtil.accDiv(upfrontRate, 100)).toFixed(2));
        if (calculatedAmount !== upfrontAmount) {
          this.bulkForm.patchValue({ upfront_payment_amount: calculatedAmount }, { emitEvent: false });
        }
      } else if (upfrontAmount !== null && upfrontAmount !== undefined && upfrontAmount !== '') {
        // 如果预收款有值，计算预收比例
        const calculatedRate = Number(this._flcUtil.accMul(this._flcUtil.accDiv(upfrontAmount, totalAmount), 100).toFixed(2));
        if (calculatedRate !== upfrontRate) {
          this.bulkForm.patchValue({ upfront_payment_rate: calculatedRate }, { emitEvent: false });
        }
      }
    } else {
      // 如果总金额为0，清空预收款和预收比例
      // this.bulkForm.patchValue(
      //   {
      //     upfront_payment_amount: null,
      //     upfront_payment_rate: null,
      //   },
      //   { emitEvent: false }
      // );
    }
  }

  // 监听预收比例变化
  onUpfrontRateChange(value: number) {
    if (value === null || value === undefined) {
      // this.bulkForm.patchValue({ upfront_payment_amount: null }, { emitEvent: false });
      return;
    }
    const totalAmount = this.calculateTotalAmount();
    if (totalAmount > 0) {
      const calculatedAmount = Number(this._flcUtil.accMul(totalAmount, this._flcUtil.accDiv(value, 100)).toFixed(2));
      this.bulkForm.patchValue({ upfront_payment_amount: calculatedAmount }, { emitEvent: false });
    }
  }

  // 监听预收款变化
  onUpfrontAmountChange(value: number) {
    if (value === null || value === undefined) {
      // this.bulkForm.patchValue({ upfront_payment_rate: null }, { emitEvent: false });
      return;
    }
    const totalAmount = this.calculateTotalAmount();
    if (totalAmount > 0) {
      const calculatedRate = Number(this._flcUtil.accMul(this._flcUtil.accDiv(value, totalAmount), 100).toFixed(2));
      this.bulkForm.patchValue({ upfront_payment_rate: calculatedRate }, { emitEvent: false });
    }
  }
}
